"""
验证码提取模块
从邮件内容中自动识别和提取各种格式的验证码
"""

import re
import logging
from typing import List, Dict, Optional, Tuple
from bs4 import BeautifulSoup
import config

# 设置日志
logging.basicConfig(level=getattr(logging, config.LOG_LEVEL), format=config.LOG_FORMAT)
logger = logging.getLogger(__name__)


class CodeExtractor:
    """验证码提取器"""

    def __init__(self):
        # 验证码匹配模式（按优先级排序）
        self.patterns = [
            # 明确标识的验证码
            (r'验证码[：:\s]*([A-Z0-9]{4,8})', '中文验证码标识'),
            (r'verification\s*code[：:\s]*([A-Z0-9]{4,8})', '英文验证码标识'),
            (r'code[：:\s]*([A-Z0-9]{4,8})', '简单code标识'),
            (r'pin[：:\s]*([A-Z0-9]{4,8})', 'PIN码标识'),
            (r'otp[：:\s]*([A-Z0-9]{4,8})', 'OTP标识'),

            # 常见格式的验证码
            (r'\b([A-Z0-9]{6})\b', '6位字母数字组合'),
            (r'\b(\d{6})\b', '6位纯数字'),
            (r'\b([A-Z0-9]{4})\b', '4位字母数字组合'),
            (r'\b(\d{4})\b', '4位纯数字'),
            (r'\b([A-Z0-9]{8})\b', '8位字母数字组合'),
            (r'\b(\d{5})\b', '5位纯数字'),

            # 特殊格式
            (r'(\d{3}-\d{3})', '带连字符的6位数字'),
            (r'([A-Z]{2}\d{4})', '2字母+4数字'),
            (r'(\d{4}[A-Z]{2})', '4数字+2字母'),
        ]

        # 排除的常见词汇（避免误识别）
        self.exclude_words = {
            '2024', '2023', '2025', 'HTTP', 'HTTPS', 'HTML', 'HEAD', 'BODY',
            'META', 'LINK', 'SPAN', 'FONT', 'SIZE', 'TYPE', 'NAME', 'HREF',
            'MAIL', 'FROM', 'SENT', 'DATE', 'TIME', 'YEAR', 'WEEK', 'HOUR',
            'MINS', 'SECS', 'DAYS', 'TEMP', 'PLUS', 'USER', 'ADMIN'
        }

    def extract_from_text(self, text: str) -> List[Dict]:
        """从纯文本中提取验证码"""
        if not text:
            return []

        cleaned_text = self._clean_text(text)
        codes = []

        for pattern, description in self.patterns:
            matches = re.finditer(pattern, cleaned_text, re.IGNORECASE)

            for match in matches:
                code = match.group(1) if match.groups() else match.group(0)
                code = code.upper().strip()

                if code not in self.exclude_words and len(code) >= 4:
                    confidence = self._calculate_confidence(code, description, cleaned_text)

                    codes.append({
                        'code': code,
                        'pattern': description,
                        'confidence': confidence,
                        'position': match.start()
                    })

        unique_codes = self._deduplicate_codes(codes)
        return sorted(unique_codes, key=lambda x: x['confidence'], reverse=True)

    def extract_from_html(self, html: str) -> List[Dict]:
        """从HTML内容中提取验证码"""
        if not html:
            return []

        try:
            soup = BeautifulSoup(html, 'lxml')

            for script in soup(["script", "style"]):
                script.decompose()

            text = soup.get_text()
            html_codes = self._extract_from_html_elements(soup)
            text_codes = self.extract_from_text(text)

            all_codes = html_codes + text_codes
            unique_codes = self._deduplicate_codes(all_codes)

            return sorted(unique_codes, key=lambda x: x['confidence'], reverse=True)

        except Exception as e:
            logger.error(f"HTML解析失败: {e}")
            return self.extract_from_text(html)

    def extract_from_email(self, email_data: Dict) -> List[Dict]:
        """从邮件数据中提取验证码"""
        codes = []

        # 从邮件主题中提取
        subject = email_data.get('subject', '')
        if subject:
            subject_codes = self.extract_from_text(subject)
            for code in subject_codes:
                code['source'] = 'subject'
                codes.append(code)

        # 从邮件正文中提取
        body_html = email_data.get('body_html', '')
        body_text = email_data.get('body_text', '')

        if body_html:
            html_codes = self.extract_from_html(body_html)
            for code in html_codes:
                code['source'] = 'body_html'
                codes.append(code)
        elif body_text:
            text_codes = self.extract_from_text(body_text)
            for code in text_codes:
                code['source'] = 'body_text'
                codes.append(code)

        unique_codes = self._deduplicate_codes(codes)
        return sorted(unique_codes, key=lambda x: x['confidence'], reverse=True)

    def _clean_text(self, text: str) -> str:
        """清理文本内容"""
        if not text:
            return ""

        text = re.sub(r'\s+', ' ', text)
        text = re.sub(r'&[a-zA-Z0-9#]+;', ' ', text)

        return text.strip()

    def _extract_from_html_elements(self, soup: BeautifulSoup) -> List[Dict]:
        """从特定HTML元素中提取验证码"""
        codes = []

        selectors = [
            'span[style*="font-weight:bold"]',
            'strong', 'b',
            '.verification-code', '.code', '.pin',
            'td[style*="font-size"]',
            'p[style*="font-weight"]'
        ]

        for selector in selectors:
            try:
                elements = soup.select(selector)
                for element in elements:
                    text = element.get_text().strip()
                    if text:
                        element_codes = self.extract_from_text(text)
                        for code in element_codes:
                            code['confidence'] += 0.2
                            codes.append(code)
            except Exception:
                continue

        return codes

    def _calculate_confidence(self, code: str, pattern_desc: str, context: str) -> float:
        """计算验证码的置信度"""
        confidence = 0.5

        if '验证码标识' in pattern_desc or 'verification' in pattern_desc:
            confidence += 0.4
        elif 'code标识' in pattern_desc or 'PIN码' in pattern_desc:
            confidence += 0.3
        elif '6位' in pattern_desc:
            confidence += 0.2
        elif '4位' in pattern_desc:
            confidence += 0.1

        context_lower = context.lower()
        if any(keyword in context_lower for keyword in ['验证码', 'verification', 'code', 'pin', 'otp']):
            confidence += 0.2

        if code.isdigit():
            confidence += 0.1
        if len(code) == 6:
            confidence += 0.1

        return min(confidence, 1.0)

    def _deduplicate_codes(self, codes: List[Dict]) -> List[Dict]:
        """去除重复的验证码"""
        seen = set()
        unique_codes = []

        for code_info in codes:
            code = code_info['code']
            if code not in seen:
                seen.add(code)
                unique_codes.append(code_info)

        return unique_codes


# 测试函数
def test_extractor():
    """测试验证码提取功能"""
    extractor = CodeExtractor()

    # 测试文本
    test_texts = [
        "您的验证码是：123456，请在10分钟内使用。",
        "Your verification code is: ABC123",
        "验证码 456789 有效期5分钟",
        "Please use code 789012 to verify your account",
        "PIN: 5678",
        "<p>验证码：<strong>987654</strong></p>",
    ]

    for i, text in enumerate(test_texts, 1):
        print(f"\n测试 {i}: {text}")
        if '<' in text:
            codes = extractor.extract_from_html(text)
        else:
            codes = extractor.extract_from_text(text)

        for code in codes:
            print(f"  发现验证码: {code['code']} (置信度: {code['confidence']:.2f}, 模式: {code['pattern']})")


if __name__ == "__main__":
    test_extractor()
