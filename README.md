# TempMail 验证码获取工具

一个基于 PySide6 的 GUI 应用程序，用于从 tempmail.plus 临时邮箱获取验证码。

## 功能特性

- 🔍 自动从邮件内容中提取验证码
- 🎲 随机邮箱地址生成器（@aicgbot.com 后缀）
- 📊 显示获取进度和状态
- 🚫 每日使用次数限制（50次/天）
- 🎨 简洁易用的图形界面
- 🔒 内置邮箱PIN码保护

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

1. 运行应用程序：
```bash
python main.py
```

2. 界面会显示一个随机生成的 @aicgbot.com 邮箱地址（仅用于展示）
3. 点击 🎲 按钮可以生成新的随机邮箱地址
4. 点击"获取邮件"按钮获取验证码
5. 验证码会自动从邮件内容中提取并显示

## 工作原理

- **显示邮箱**：界面显示随机生成的 @aicgbot.com 邮箱（仅用于展示）
- **实际邮箱**：程序内部使用配置的真实 tempmail 邮箱获取邮件
- **验证码提取**：支持多种验证码格式的自动识别和提取
- **使用限制**：每日最多使用50次，防止滥用

## 项目结构

```
├── main.py                 # 应用程序入口
├── main_window.py          # 主界面（包含随机邮箱生成器）
├── tempmail_api.py         # TempMail API 接口
├── code_extractor.py       # 验证码提取模块
├── usage_limiter.py        # 使用次数限制
├── config.py              # 配置文件
├── requirements.txt       # 依赖包列表
└── usage_data.json        # 使用记录（自动生成）
```

## 配置说明

在 `config.py` 中可以修改以下配置：

- `DAILY_USAGE_LIMIT`: 每日使用次数限制
- `DEFAULT_EPIN`: 邮箱PIN码
- `REAL_EMAIL`: 实际用于获取邮件的邮箱地址
- `WINDOW_WIDTH/HEIGHT`: 窗口大小

## 验证码识别

支持识别以下格式的验证码：
- 4-8位纯数字
- 4-8位字母数字组合
- 带有"验证码"、"verification code"等标识的验证码
- HTML邮件中的加粗验证码

## 注意事项

- 本工具仅用于合法用途
- 请遵守 tempmail.plus 的使用条款
- 每日使用次数有限制，请合理使用
- 显示的邮箱地址仅用于展示，实际获取使用内置配置的邮箱
