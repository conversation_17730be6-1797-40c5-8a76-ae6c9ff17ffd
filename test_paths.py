#!/usr/bin/env python3
"""
测试应用程序路径功能
验证文件是否正确存储在临时目录中
"""

import os
import sys
import tempfile
from app_paths import (
    get_app_data_dir, 
    get_app_file_path, 
    get_log_file_path,
    get_auth_file_path,
    get_usage_file_path,
    get_resource_path,
    get_app_info
)

def test_paths():
    """测试路径功能"""
    print("=" * 60)
    print("应用程序路径测试")
    print("=" * 60)
    
    # 显示应用程序信息
    app_info = get_app_info()
    print("\n应用程序信息:")
    for key, value in app_info.items():
        print(f"  {key}: {value}")
    
    print(f"\n当前工作目录: {os.getcwd()}")
    print(f"系统临时目录: {tempfile.gettempdir()}")
    print(f"是否为打包应用: {getattr(sys, 'frozen', False)}")
    
    # 测试各种路径
    print("\n路径测试:")
    paths_to_test = [
        ("应用数据目录", get_app_data_dir()),
        ("日志文件路径", get_log_file_path()),
        ("授权文件路径", get_auth_file_path()),
        ("使用数据文件路径", get_usage_file_path()),
        ("图标资源路径", get_resource_path("icon.png")),
    ]
    
    for name, path in paths_to_test:
        print(f"  {name}: {path}")
        if os.path.exists(path):
            print(f"    ✓ 文件/目录存在")
        else:
            print(f"    ✗ 文件/目录不存在")
    
    # 测试文件创建
    print("\n文件创建测试:")
    test_files = [
        ("测试日志", get_log_file_path()),
        ("测试授权", get_auth_file_path()),
        ("测试使用数据", get_usage_file_path()),
    ]
    
    for name, file_path in test_files:
        try:
            # 创建测试文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(f"# {name} - 测试文件\n")
                f.write(f"# 创建时间: {os.path.getctime(file_path) if os.path.exists(file_path) else 'N/A'}\n")
            
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                print(f"  ✓ {name}: 创建成功 ({file_size} bytes)")
            else:
                print(f"  ✗ {name}: 创建失败")
                
        except Exception as e:
            print(f"  ✗ {name}: 创建失败 - {e}")
    
    # 检查目录权限
    print("\n目录权限测试:")
    app_data_dir = get_app_data_dir()
    try:
        # 测试写权限
        test_file = os.path.join(app_data_dir, "test_write.tmp")
        with open(test_file, 'w') as f:
            f.write("test")
        os.remove(test_file)
        print(f"  ✓ 应用数据目录可写: {app_data_dir}")
    except Exception as e:
        print(f"  ✗ 应用数据目录不可写: {e}")
    
    # 显示环境变量
    print("\n相关环境变量:")
    env_vars = ['TEMP', 'TMP', 'TMPDIR', 'HOME', 'USERPROFILE']
    for var in env_vars:
        value = os.environ.get(var, 'N/A')
        print(f"  {var}: {value}")
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)

if __name__ == "__main__":
    test_paths()
    input("\n按回车键退出...")
