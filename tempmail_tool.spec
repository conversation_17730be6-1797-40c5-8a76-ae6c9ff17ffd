# -*- mode: python ; coding: utf-8 -*-

"""
TempMail验证码获取工具 PyInstaller配置文件
支持Windows和macOS平台的独立应用程序打包
"""

import os
import sys
from PyInstaller.utils.hooks import collect_data_files, collect_submodules

# 应用程序基本信息
APP_NAME = 'TempMail验证码获取工具'
APP_VERSION = '1.0.0'
APP_AUTHOR = 'TempMail Tools'

# 获取当前目录
current_dir = os.path.dirname(os.path.abspath(SPEC))

# 主程序入口
main_script = os.path.join(current_dir, 'main.py')

# 收集数据文件
datas = []

# 添加图标文件
icon_files = ['icon.png', 'icon.ico']
for icon_file in icon_files:
    icon_path = os.path.join(current_dir, icon_file)
    if os.path.exists(icon_path):
        datas.append((icon_path, '.'))

# 添加配置文件（如果需要）
config_files = ['config.py']
for config_file in config_files:
    config_path = os.path.join(current_dir, config_file)
    if os.path.exists(config_path):
        datas.append((config_path, '.'))

# 收集PySide6相关数据
try:
    pyside6_datas = collect_data_files('PySide6')
    datas.extend(pyside6_datas)
except:
    pass

# 隐藏导入模块
hiddenimports = [
    'PySide6.QtCore',
    'PySide6.QtGui', 
    'PySide6.QtWidgets',
    'PySide6.QtNetwork',
    'requests',
    'bs4',
    'lxml',
    'json',
    'logging',
    'traceback',
    'urllib3',
    'certifi',
    'charset_normalizer',
    'idna'
]

# 收集子模块
try:
    pyside6_modules = collect_submodules('PySide6')
    hiddenimports.extend(pyside6_modules)
except:
    pass

# 排除的模块（减小打包体积）
excludes = [
    'tkinter',
    'matplotlib',
    'numpy',
    'pandas',
    'scipy',
    'PIL',
    'cv2',
    'tensorflow',
    'torch',
    'jupyter',
    'IPython',
    'notebook',
    'pytest',
    'unittest',
    'doctest'
]

# 分析配置
a = Analysis(
    [main_script],
    pathex=[current_dir],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=excludes,
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

# 去重和优化
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# 根据平台设置不同的配置
if sys.platform.startswith('win'):
    # Windows配置
    exe = EXE(
        pyz,
        a.scripts,
        a.binaries,
        a.zipfiles,
        a.datas,
        [],
        name=APP_NAME,
        debug=False,
        bootloader_ignore_signals=False,
        strip=False,
        upx=True,
        upx_exclude=[],
        runtime_tmpdir=None,
        console=False,  # 不显示控制台窗口
        disable_windowed_traceback=False,
        argv_emulation=False,
        target_arch=None,
        codesign_identity=None,
        entitlements_file=None,
        icon=os.path.join(current_dir, 'icon.ico') if os.path.exists(os.path.join(current_dir, 'icon.ico')) else None,
        version_file=None,
    )
    
elif sys.platform.startswith('darwin'):
    # macOS配置
    exe = EXE(
        pyz,
        a.scripts,
        [],
        exclude_binaries=True,
        name=APP_NAME,
        debug=False,
        bootloader_ignore_signals=False,
        strip=False,
        upx=True,
        console=False,
        disable_windowed_traceback=False,
        argv_emulation=False,
        target_arch=None,
        codesign_identity=None,
        entitlements_file=None,
    )
    
    coll = COLLECT(
        exe,
        a.binaries,
        a.zipfiles,
        a.datas,
        strip=False,
        upx=True,
        upx_exclude=[],
        name=APP_NAME,
    )
    
    # 创建macOS应用程序包
    app = BUNDLE(
        coll,
        name=f'{APP_NAME}.app',
        icon=os.path.join(current_dir, 'icon.png') if os.path.exists(os.path.join(current_dir, 'icon.png')) else None,
        bundle_identifier=f'com.tempmailtools.{APP_NAME.lower().replace(" ", "")}',
        version=APP_VERSION,
        info_plist={
            'CFBundleName': APP_NAME,
            'CFBundleDisplayName': APP_NAME,
            'CFBundleVersion': APP_VERSION,
            'CFBundleShortVersionString': APP_VERSION,
            'NSHighResolutionCapable': True,
            'NSRequiresAquaSystemAppearance': False,
        },
    )

else:
    # Linux或其他平台配置
    exe = EXE(
        pyz,
        a.scripts,
        a.binaries,
        a.zipfiles,
        a.datas,
        [],
        name=APP_NAME,
        debug=False,
        bootloader_ignore_signals=False,
        strip=False,
        upx=True,
        upx_exclude=[],
        runtime_tmpdir=None,
        console=False,
        disable_windowed_traceback=False,
        argv_emulation=False,
        target_arch=None,
        codesign_identity=None,
        entitlements_file=None,
    )
