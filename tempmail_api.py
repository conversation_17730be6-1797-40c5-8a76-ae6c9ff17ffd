"""
TempMail API 模块
用于与 tempmail.plus 服务进行交互
"""

import requests
import json
import logging
from typing import List, Dict, Optional
from urllib.parse import quote
import config

# 设置日志
logging.basicConfig(level=getattr(logging, config.LOG_LEVEL), format=config.LOG_FORMAT)
logger = logging.getLogger(__name__)


class TempMailAPI:
    """TempMail API 客户端"""
    
    def __init__(self):
        self.base_url = config.TEMPMAIL_BASE_URL
        self.api_url = config.TEMPMAIL_API_URL
        self.session = requests.Session()
        
        # 设置请求头，模拟浏览器
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Referer': f'{self.base_url}/zh/',
            'Origin': self.base_url,
        })
    
    def get_emails(self, email: str, limit: int = None, epin: str = "123456", first_id: int = 0) -> Dict:
        """
        获取指定邮箱的邮件列表

        Args:
            email: 邮箱地址
            limit: 邮件数量限制
            epin: 邮箱PIN码（如果设置了的话）
            first_id: 起始邮件ID（用于分页）

        Returns:
            包含邮件列表的字典
        """
        if limit is None:
            limit = config.EMAIL_LIMIT

        # 构建查询参数
        params = {
            'email': email,  # 不需要URL编码，requests会自动处理
            'epin': epin
        }

        # 添加可选参数
        if first_id is not None:
            params['first_id'] = first_id
        if limit is not None:
            params['limit'] = limit

        try:
            logger.info(f"正在获取邮箱 {email} 的邮件...")

            # 使用正确的headers
            headers = {
                'accept': 'application/json, text/javascript, */*; q=0.01',
                'accept-language': 'zh,zh-CN;q=0.9',
                'sec-ch-ua': '"Google Chrome";v="125", "Chromium";v="125", "Not.A/Brand";v="24"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-origin',
                'x-requested-with': 'XMLHttpRequest',
                'referer': f'{self.base_url}/zh/'
            }

            response = self.session.get(
                self.api_url,
                params=params,
                headers=headers,
                timeout=config.REQUEST_TIMEOUT
            )
            
            # 检查响应状态
            response.raise_for_status()

            # 解析JSON响应
            data = response.json()

            # 处理tempmail.plus的响应格式
            mail_list = data.get('mail_list', [])
            logger.info(f"成功获取 {len(mail_list)} 封邮件")

            # 统一返回格式，保持向后兼容
            return {
                'mails': mail_list,  # 为了兼容现有代码
                'mail_list': mail_list,  # 原始格式
                'count': data.get('count', len(mail_list)),
                'first_id': data.get('first_id'),
                'last_id': data.get('last_id'),
                'more': data.get('more', False),
                'result': data.get('result', True)
            }
            
        except requests.exceptions.RequestException as e:
            logger.error(f"请求失败: {e}")
            return {"error": f"网络请求失败: {str(e)}", "mails": []}
        except json.JSONDecodeError as e:
            logger.error(f"JSON解析失败: {e}")
            return {"error": "响应格式错误", "mails": []}
        except Exception as e:
            logger.error(f"未知错误: {e}")
            return {"error": f"未知错误: {str(e)}", "mails": []}
    
    def get_email_content(self, email_id: str, email: str, epin: str = "123456") -> Dict:
        """
        获取指定邮件的详细内容

        Args:
            email_id: 邮件ID
            email: 邮箱地址
            epin: 邮箱PIN码

        Returns:
            邮件详细内容
        """
        try:
            # 构建邮件详情URL
            detail_url = f"{self.base_url}/api/mails/{email_id}"

            # 查询参数
            params = {
                'email': email,
                'epin': epin
            }

            # 请求头
            headers = {
                'accept': 'application/json, text/javascript, */*; q=0.01',
                'accept-language': 'zh,zh-CN;q=0.9',
                'sec-ch-ua': '"Google Chrome";v="125", "Chromium";v="125", "Not.A/Brand";v="24"',
                'sec-ch-ua-mobile': '?0',
                'sec-ch-ua-platform': '"Windows"',
                'sec-fetch-dest': 'empty',
                'sec-fetch-mode': 'cors',
                'sec-fetch-site': 'same-origin',
                'x-requested-with': 'XMLHttpRequest',
                'referer': f'{self.base_url}/zh/'
            }

            logger.info(f"正在获取邮件 {email_id} 的详细内容...")

            response = self.session.get(
                detail_url,
                params=params,
                headers=headers,
                timeout=config.REQUEST_TIMEOUT
            )

            response.raise_for_status()
            data = response.json()

            logger.info(f"成功获取邮件详情，主题: {data.get('subject', '无主题')}")
            return data

        except Exception as e:
            logger.error(f"获取邮件详情失败: {e}")
            return {"error": f"获取邮件详情失败: {str(e)}"}
    
    def validate_email_format(self, email: str) -> bool:
        """
        验证邮箱格式是否正确
        
        Args:
            email: 邮箱地址
            
        Returns:
            是否为有效的邮箱格式
        """
        import re
        
        # 基本的邮箱格式验证
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        
        if not re.match(pattern, email):
            return False
            
        # 检查是否为支持的域名
        supported_domains = [
            'mailto.plus', 'fexpost.com', 'fexbox.org', 
            'mailbox.in.ua', 'rover.info', 'chitthi.in',
            'fextemp.com', 'any.pink', 'merepost.com'
        ]
        
        domain = email.split('@')[1].lower()
        return domain in supported_domains
    
    def close(self):
        """关闭会话"""
        if self.session:
            self.session.close()


# 测试函数
def test_api():
    """测试API功能"""
    api = TempMailAPI()
    
    # 测试邮箱格式验证
    test_emails = [
        "<EMAIL>",
        "<EMAIL>",
        "<EMAIL>"
    ]
    
    for email in test_emails:
        is_valid = api.validate_email_format(email)
        print(f"邮箱 {email} 格式{'有效' if is_valid else '无效'}")
    
    # 测试获取邮件（使用有效的测试邮箱）
    test_email = "<EMAIL>"
    if api.validate_email_format(test_email):
        result = api.get_emails(test_email)
        print(f"获取邮件结果: {result}")
    
    api.close()


if __name__ == "__main__":
    test_api()
