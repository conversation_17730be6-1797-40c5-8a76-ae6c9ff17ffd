@echo off
echo ========================================
echo Testing Packaged Application Paths
echo ========================================
echo.

echo Current directory: %CD%
echo Temp directory: %TEMP%
echo.

echo Starting packaged application for 10 seconds...
echo This will test if files are created in temp directory.
echo.

:: Start the application and let it run for a few seconds
start "" "dist\TempMailTool.exe"

:: Wait a bit for the application to start and create files
timeout /t 5 /nobreak >nul

echo Checking for files in temp directory...
echo.

:: Check for files in temp directory
set TEMP_APP_DIR=%TEMP%\TempMailTool
echo Looking in: %TEMP_APP_DIR%

if exist "%TEMP_APP_DIR%" (
    echo ✓ Application temp directory exists: %TEMP_APP_DIR%
    echo.
    echo Files in temp directory:
    dir "%TEMP_APP_DIR%" /b
    echo.
    
    if exist "%TEMP_APP_DIR%\tempmail_tool.log" (
        echo ✓ Log file found in temp directory
        echo Last few lines of log:
        powershell "Get-Content '%TEMP_APP_DIR%\tempmail_tool.log' -Tail 5"
    ) else (
        echo ✗ Log file not found in temp directory
    )
    
) else (
    echo ✗ Application temp directory not found
    echo Files in current directory:
    dir *.log *.json /b 2>nul
)

echo.
echo ========================================
echo Test completed
echo ========================================
echo.
echo Note: The application may still be running.
echo You can close it manually if needed.
echo.
pause
