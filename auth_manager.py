#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
授权状态管理模块
负责保存和验证用户的授权状态
"""

import os
import json
import hashlib
import logging
from datetime import datetime, timedelta
from typing import Optional

import config

logger = logging.getLogger(__name__)

class AuthManager:
    """授权状态管理器"""
    
    def __init__(self):
        self.auth_file = "auth_status.json"
        self.auth_data = self._load_auth_data()
    
    def _load_auth_data(self) -> dict:
        """加载授权数据"""
        try:
            if os.path.exists(self.auth_file):
                with open(self.auth_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    logger.info("已加载授权状态数据")
                    return data
        except Exception as e:
            logger.error(f"加载授权数据失败: {e}")
        
        return {}
    
    def _save_auth_data(self) -> bool:
        """保存授权数据"""
        try:
            with open(self.auth_file, 'w', encoding='utf-8') as f:
                json.dump(self.auth_data, f, ensure_ascii=False, indent=2)
            logger.info("授权状态已保存")
            return True
        except Exception as e:
            logger.error(f"保存授权数据失败: {e}")
            return False
    
    def _get_auth_hash(self, auth_code: str) -> str:
        """生成授权码的哈希值"""
        # 使用SHA256生成哈希，增加一些盐值
        salt = "augment_code_auth_salt_2025"
        return hashlib.sha256(f"{auth_code}{salt}".encode()).hexdigest()
    
    def _is_auth_expired(self, auth_time: str, valid_days: int = 30) -> bool:
        """检查授权是否过期"""
        try:
            auth_datetime = datetime.fromisoformat(auth_time)
            expiry_time = auth_datetime + timedelta(days=valid_days)
            return datetime.now() > expiry_time
        except Exception as e:
            logger.error(f"检查授权过期时间失败: {e}")
            return True
    
    def save_auth_success(self, auth_code: str) -> bool:
        """保存授权成功状态"""
        try:
            auth_hash = self._get_auth_hash(auth_code)
            current_time = datetime.now().isoformat()
            
            self.auth_data = {
                "auth_hash": auth_hash,
                "auth_time": current_time,
                "last_access": current_time,
                "version": "1.0"
            }
            
            success = self._save_auth_data()
            if success:
                logger.info("授权状态保存成功")
            return success
            
        except Exception as e:
            logger.error(f"保存授权状态失败: {e}")
            return False
    
    def is_authorized(self) -> bool:
        """检查是否已授权"""
        try:
            if not self.auth_data:
                logger.info("未找到授权数据")
                return False
            
            # 检查必要字段
            if "auth_hash" not in self.auth_data or "auth_time" not in self.auth_data:
                logger.info("授权数据格式不完整")
                return False
            
            # 检查是否过期（30天有效期）
            if self._is_auth_expired(self.auth_data["auth_time"], valid_days=30):
                logger.info("授权已过期")
                self.clear_auth()
                return False
            
            # 验证授权码哈希
            expected_hash = self._get_auth_hash(config.AUTH_CODE)
            if self.auth_data["auth_hash"] != expected_hash:
                logger.info("授权码已变更，需要重新授权")
                self.clear_auth()
                return False
            
            # 更新最后访问时间
            self.auth_data["last_access"] = datetime.now().isoformat()
            self._save_auth_data()
            
            logger.info("授权验证通过")
            return True
            
        except Exception as e:
            logger.error(f"检查授权状态失败: {e}")
            return False
    
    def clear_auth(self) -> bool:
        """清除授权状态"""
        try:
            self.auth_data = {}
            if os.path.exists(self.auth_file):
                os.remove(self.auth_file)
            logger.info("授权状态已清除")
            return True
        except Exception as e:
            logger.error(f"清除授权状态失败: {e}")
            return False
    
    def get_auth_info(self) -> Optional[dict]:
        """获取授权信息"""
        if not self.is_authorized():
            return None
        
        try:
            auth_time = datetime.fromisoformat(self.auth_data["auth_time"])
            last_access = datetime.fromisoformat(self.auth_data["last_access"])
            expiry_time = auth_time + timedelta(days=30)
            
            return {
                "auth_time": auth_time.strftime("%Y-%m-%d %H:%M:%S"),
                "last_access": last_access.strftime("%Y-%m-%d %H:%M:%S"),
                "expiry_time": expiry_time.strftime("%Y-%m-%d %H:%M:%S"),
                "days_remaining": (expiry_time - datetime.now()).days
            }
        except Exception as e:
            logger.error(f"获取授权信息失败: {e}")
            return None


# 全局授权管理器实例
auth_manager = AuthManager()


def is_authorized() -> bool:
    """快捷函数：检查是否已授权"""
    return auth_manager.is_authorized()


def save_auth_success(auth_code: str) -> bool:
    """快捷函数：保存授权成功状态"""
    return auth_manager.save_auth_success(auth_code)


def clear_auth() -> bool:
    """快捷函数：清除授权状态"""
    return auth_manager.clear_auth()
