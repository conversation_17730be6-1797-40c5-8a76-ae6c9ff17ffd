"""
主功能页面组件
"""

import random
import string
from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QGroupBox, 
                               QLineEdit, QPushButton, QLabel, QTextEdit, 
                               QProgressBar, QMessageBox)
from PySide6.QtCore import Qt, Signal
import config


class MainPage(QWidget):
    """主功能页面"""
    
    # 信号：开始获取邮件
    fetch_requested = Signal(str)  # 传递邮箱地址
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
        self.generate_random_email()
    
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(8)
        layout.setContentsMargins(12, 12, 12, 12)
        
        # 创建各个组件
        self.create_input_section(layout)
        self.create_progress_section(layout)
        self.create_content_section(layout)
    
    def create_input_section(self, parent_layout):
        """创建输入区域"""
        input_group = QGroupBox("🔧 激活额度")
        input_layout = QVBoxLayout(input_group)
        input_layout.setSpacing(6)
        input_layout.setContentsMargins(10, 12, 10, 10)
        
        # 邮箱输入行
        email_layout = QHBoxLayout()
        email_layout.setSpacing(6)

        # 邮箱输入框
        self.email_input = QLineEdit()
        self.email_input.setPlaceholderText("激活额度邮箱")
        self.email_input.setReadOnly(True)
        
        # 随机生成按钮
        self.generate_button = QPushButton("🎲")
        self.generate_button.setToolTip("生成随机邮箱")
        self.generate_button.setFixedSize(28, 28)
        self.generate_button.clicked.connect(self.generate_random_email)
        
        email_layout.addWidget(self.email_input)
        email_layout.addWidget(self.generate_button)
        
        # 使用次数显示
        self.usage_label = QLabel("今日已使用: 0/50 次，剩余: 50 次")
        self.usage_label.setStyleSheet("color: #AAA; font-size: 10px; margin-top: 3px;")

        # 获取按钮
        self.fetch_button = QPushButton("🚀 获取额度")
        self.fetch_button.clicked.connect(self.on_fetch_clicked)
        
        # 添加到布局
        input_layout.addLayout(email_layout)
        input_layout.addWidget(self.usage_label)
        input_layout.addWidget(self.fetch_button)
        
        parent_layout.addWidget(input_group)
    
    def create_progress_section(self, parent_layout):
        """创建进度区域"""
        progress_group = QGroupBox("📊 进度")
        progress_layout = QVBoxLayout(progress_group)
        progress_layout.setSpacing(6)
        progress_layout.setContentsMargins(10, 12, 10, 10)
        
        # 状态标签
        self.status_label = QLabel("🟢 就绪")
        self.status_label.setStyleSheet("font-weight: bold; color: #4CAF50; font-size: 11px;")
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        
        progress_layout.addWidget(self.status_label)
        progress_layout.addWidget(self.progress_bar)
        
        parent_layout.addWidget(progress_group)
    
    def create_content_section(self, parent_layout):
        """创建内容显示区域"""
        # 验证码显示
        codes_group = QGroupBox("🔑 验证码")
        codes_layout = QVBoxLayout(codes_group)
        codes_layout.setSpacing(6)
        codes_layout.setContentsMargins(10, 12, 10, 10)

        self.codes_display = QTextEdit()
        self.codes_display.setMaximumHeight(70)
        self.codes_display.setPlaceholderText("🔍 验证码将在这里显示...")
        self.codes_display.setReadOnly(True)
        
        codes_layout.addWidget(self.codes_display)
        
        # 邮件详情显示
        emails_group = QGroupBox("📧 详情")
        emails_layout = QVBoxLayout(emails_group)
        emails_layout.setSpacing(6)
        emails_layout.setContentsMargins(10, 12, 10, 10)

        self.emails_display = QTextEdit()
        self.emails_display.setMaximumHeight(100)
        self.emails_display.setPlaceholderText("📬 内容详情...")
        self.emails_display.setReadOnly(True)
        
        emails_layout.addWidget(self.emails_display)
        
        parent_layout.addWidget(codes_group)
        parent_layout.addWidget(emails_group)
    
    def generate_random_email(self):
        """生成随机邮箱地址"""
        # 生成随机用户名（6-10位字母数字）
        username_length = random.randint(6, 10)
        username = ''.join(random.choices(string.ascii_lowercase + string.digits, k=username_length))
        
        # 使用固定域名
        email = f"{username}@aicgbot.com"
        self.email_input.setText(email)
    
    def on_fetch_clicked(self):
        """处理获取按钮点击"""
        display_email = self.email_input.text().strip()
        if not display_email:
            QMessageBox.warning(self, "警告", "请先生成邮箱地址")
            return
        
        # 发射信号，传递显示的邮箱地址
        self.fetch_requested.emit(display_email)
    
    def update_usage_display(self, stats):
        """更新使用次数显示"""
        usage_text = f"今日已使用: {stats['today_usage']}/{stats['daily_limit']} 次，剩余: {stats['remaining']} 次"
        self.usage_label.setText(usage_text)
        
        # 如果没有剩余次数，禁用按钮
        if stats['remaining'] <= 0:
            self.fetch_button.setEnabled(False)
            self.fetch_button.setText("今日次数已用完")
        else:
            self.fetch_button.setEnabled(True)
            self.fetch_button.setText("🚀 获取额度")
    
    def update_status(self, message):
        """更新状态显示"""
        self.status_label.setText(message)
    
    def show_progress(self, visible=True):
        """显示/隐藏进度条"""
        self.progress_bar.setVisible(visible)
        if visible:
            self.progress_bar.setRange(0, 0)  # 无限进度条
    
    def update_codes_display(self, codes_text):
        """更新验证码显示"""
        self.codes_display.setText(codes_text)
    
    def update_emails_display(self, emails_text):
        """更新邮件详情显示"""
        self.emails_display.setText(emails_text)
    
    def set_fetch_enabled(self, enabled):
        """设置获取按钮是否可用"""
        self.fetch_button.setEnabled(enabled)
