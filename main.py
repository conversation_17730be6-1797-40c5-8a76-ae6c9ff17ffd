"""
TempMail 验证码获取工具
主程序入口
"""

import sys
import os
import logging
import traceback
from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import Qt
from PySide6.QtGui import QIcon

import config
from main_window import MainWindow
from app_paths import get_log_file_path, get_resource_path, cleanup_old_files, get_app_info

# 设置日志
def setup_logging():
    """设置日志配置"""
    log_format = logging.Formatter(config.LOG_FORMAT)

    # 控制台日志
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(log_format)

    # 文件日志 - 使用应用程序数据目录
    log_file_path = get_log_file_path()
    file_handler = logging.FileHandler(log_file_path, encoding='utf-8')
    file_handler.setFormatter(log_format)

    # 根日志器
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, config.LOG_LEVEL))
    root_logger.addHandler(console_handler)
    root_logger.addHandler(file_handler)


def handle_exception(exc_type, exc_value, exc_traceback):
    """全局异常处理"""
    if issubclass(exc_type, KeyboardInterrupt):
        sys.__excepthook__(exc_type, exc_value, exc_traceback)
        return
    
    logger = logging.getLogger(__name__)
    logger.critical("未捕获的异常", exc_info=(exc_type, exc_value, exc_traceback))
    
    # 显示错误对话框
    error_msg = f"程序发生未预期的错误:\n\n{exc_type.__name__}: {exc_value}"
    QMessageBox.critical(None, "程序错误", error_msg)


def check_dependencies():
    """检查依赖项"""
    missing_deps = []
    
    try:
        import PySide6
    except ImportError:
        missing_deps.append("PySide6")
    
    try:
        import requests
    except ImportError:
        missing_deps.append("requests")
    
    try:
        import bs4
    except ImportError:
        missing_deps.append("beautifulsoup4")
    
    if missing_deps:
        error_msg = f"缺少必要的依赖包:\n\n{', '.join(missing_deps)}\n\n请运行以下命令安装:\npip install {' '.join(missing_deps)}"
        print(error_msg)
        
        # 如果Qt可用，显示图形化错误
        try:
            app = QApplication([])
            QMessageBox.critical(None, "依赖错误", error_msg)
        except:
            pass
        
        return False
    
    return True


def create_application():
    """创建应用程序"""
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("TempMail验证码获取工具")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("TempMail Tools")
    
    # 设置应用程序图标 - 使用资源路径
    try:
        icon_png = get_resource_path("icon.png")
        icon_ico = get_resource_path("icon.ico")
        if os.path.exists(icon_png):
            app.setWindowIcon(QIcon(icon_png))
        elif os.path.exists(icon_ico):
            app.setWindowIcon(QIcon(icon_ico))
    except Exception:
        pass
    
    # 设置高DPI支持
    app.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    app.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    
    return app


def main():
    """主函数"""
    # 清理旧文件（如果是打包环境）
    cleanup_old_files()

    # 设置日志
    setup_logging()
    logger = logging.getLogger(__name__)

    logger.info("=" * 50)
    logger.info("TempMail 验证码获取工具启动")
    logger.info("=" * 50)

    # 记录应用程序路径信息
    app_info = get_app_info()
    logger.info("应用程序路径信息:")
    for key, value in app_info.items():
        logger.info(f"  - {key}: {value}")
    
    try:
        # 检查依赖
        if not check_dependencies():
            return 1
        
        # 创建应用程序
        app = create_application()
        
        # 设置全局异常处理
        sys.excepthook = handle_exception
        
        # 创建主窗口
        logger.info("创建主窗口...")
        main_window = MainWindow()
        
        # 显示窗口
        main_window.show()
        logger.info("主窗口已显示")
        
        # 显示启动信息
        logger.info(f"应用程序配置:")
        logger.info(f"  - 窗口大小: {config.WINDOW_WIDTH}x{config.WINDOW_HEIGHT}")
        logger.info(f"  - 每日使用限制: {config.DAILY_USAGE_LIMIT}")
        logger.info(f"  - 请求超时: {config.REQUEST_TIMEOUT}秒")
        logger.info(f"  - 日志级别: {config.LOG_LEVEL}")
        
        # 运行应用程序
        logger.info("应用程序开始运行...")
        exit_code = app.exec()
        
        logger.info(f"应用程序退出，退出码: {exit_code}")
        return exit_code
        
    except Exception as e:
        logger.critical(f"启动失败: {e}")
        logger.critical(traceback.format_exc())
        
        try:
            QMessageBox.critical(None, "启动错误", f"程序启动失败:\n\n{str(e)}")
        except:
            print(f"程序启动失败: {e}")
        
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
