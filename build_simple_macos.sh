#!/bin/bash

echo "========================================"
echo "TempMail Tool - Simple macOS Build Script"
echo "========================================"
echo

# Clean previous build
echo "Cleaning previous build..."
rm -rf dist build

# Start build process
echo "Starting build process..."
pyinstaller --clean --onedir --windowed --name="TempMailTool" --icon=icon.png main.py

# Check result
if [ -d "dist/TempMailTool.app" ]; then
    echo
    echo "========================================"
    echo "Build successful!"
    echo "========================================"
    echo "Application: dist/TempMailTool.app"
    echo "Size: $(du -sh dist/TempMailTool.app | cut -f1)"
    echo
    echo "The application is ready to use!"
    echo "You can distribute the .app bundle."
    echo
    read -p "Test the application now? (y/n): " test_choice
    if [[ "$test_choice" =~ ^[Yy]$ ]]; then
        echo "Starting application..."
        open "dist/TempMailTool.app"
    fi
    
    echo
    read -p "Create DMG installer? (y/n): " dmg_choice
    if [[ "$dmg_choice" =~ ^[Yy]$ ]]; then
        echo "Creating DMG installer..."
        hdiutil create -volname "TempMail Tool" -srcfolder "dist/TempMailTool.app" -ov -format UDZO "TempMailTool.dmg"
        if [ -f "TempMailTool.dmg" ]; then
            echo "DMG created: TempMailTool.dmg"
        fi
    fi
else
    echo
    echo "========================================"
    echo "Build failed!"
    echo "========================================"
    echo "Please check the error messages above."
fi

echo
