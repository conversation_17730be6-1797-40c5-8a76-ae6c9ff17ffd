#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试邮件详情获取和验证码提取
"""

from tempmail_api import TempMailAPI
from code_extractor import CodeExtractor
import json

def test_email_detail():
    """测试邮件详情获取"""
    
    # 初始化API和提取器
    api = TempMailAPI()
    extractor = CodeExtractor()
    
    email = "<EMAIL>"
    epin = "123456"
    
    print("🔍 正在获取邮件列表...")
    
    # 获取邮件列表
    result = api.get_emails(email, epin=epin)
    
    if 'error' in result:
        print(f"❌ 获取邮件列表失败: {result['error']}")
        return
    
    mails = result.get('mails', [])
    print(f"📧 找到 {len(mails)} 封邮件")
    
    if not mails:
        print("📭 邮箱为空")
        return
    
    # 获取第一封邮件的详情
    first_mail = mails[0]
    mail_id = first_mail.get('mail_id')
    subject = first_mail.get('subject', '无主题')
    
    print(f"\n📧 正在获取邮件详情...")
    print(f"   - 邮件ID: {mail_id}")
    print(f"   - 主题: {subject}")
    
    # 获取邮件详细内容
    detail = api.get_email_content(str(mail_id), email, epin)
    
    if 'error' in detail:
        print(f"❌ 获取邮件详情失败: {detail['error']}")
        return
    
    print(f"✅ 成功获取邮件详情")
    print(f"   - 发件人: {detail.get('from_name', '')} <{detail.get('from_mail', '')}>")
    print(f"   - 主题: {detail.get('subject', '')}")
    print(f"   - 日期: {detail.get('date', '')}")
    
    # 提取验证码
    html_content = detail.get('html', '')
    text_content = detail.get('text', '')
    
    print(f"\n🔍 正在提取验证码...")
    
    if html_content:
        print("📄 从HTML内容提取验证码...")
        codes = extractor.extract_from_html(html_content)
        print(f"HTML内容长度: {len(html_content)} 字符")
    elif text_content:
        print("📄 从文本内容提取验证码...")
        codes = extractor.extract_from_text(text_content)
        print(f"文本内容长度: {len(text_content)} 字符")
    else:
        print("❌ 没有找到邮件内容")
        return
    
    if codes:
        print(f"🔑 找到 {len(codes)} 个验证码:")
        for i, code_info in enumerate(codes, 1):
            print(f"   {i}. {code_info['code']} (置信度: {code_info['confidence']:.1%})")
    else:
        print("❌ 没有找到验证码")
        
        # 显示部分内容用于调试
        if html_content:
            print(f"\n📄 HTML内容预览 (前500字符):")
            print(html_content[:500])
        elif text_content:
            print(f"\n📄 文本内容预览 (前500字符):")
            print(text_content[:500])

if __name__ == "__main__":
    test_email_detail()
