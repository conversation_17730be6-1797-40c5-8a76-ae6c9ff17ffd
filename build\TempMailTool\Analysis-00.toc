(['E:\\ai编程开发\\augument-mail-plugin\\main.py'],
 ['E:\\ai编程开发\\augument-mail-plugin'],
 ['codecs'],
 ['C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\numpy\\_pyinstaller',
  'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\playwright\\_impl\\__pyinstaller',
  'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\_pyinstaller_hooks_contrib\\stdhooks',
  'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\_pyinstaller_hooks_contrib'],
 {},
 [],
 [],
 False,
 False,
 False,
 {},
 [],
 [],
 '3.11.7 | packaged by Anaconda, Inc. | (main, Dec 15 2023, 18:05:47) [MSC '
 'v.1916 64 bit (AMD64)]',
 [('pyi_rth_inspect',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_inspect.py',
   'PYSOURCE'),
  ('pyi_rth_pkgutil',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pkgutil.py',
   'PYSOURCE'),
  ('pyi_rth_multiprocessing',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_multiprocessing.py',
   'PYSOURCE'),
  ('pyi_rth_cryptography_openssl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\_pyinstaller_hooks_contrib\\rthooks\\pyi_rth_cryptography_openssl.py',
   'PYSOURCE'),
  ('pyi_rth_pyside6',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PyInstaller\\hooks\\rthooks\\pyi_rth_pyside6.py',
   'PYSOURCE'),
  ('main', 'E:\\ai编程开发\\augument-mail-plugin\\main.py', 'PYSOURCE')],
 [('multiprocessing.popen_forkserver',
   'D:\\ProgramData\\anaconda3\\Lib\\multiprocessing\\popen_forkserver.py',
   'PYMODULE'),
  ('multiprocessing.connection',
   'D:\\ProgramData\\anaconda3\\Lib\\multiprocessing\\connection.py',
   'PYMODULE'),
  ('multiprocessing.resource_sharer',
   'D:\\ProgramData\\anaconda3\\Lib\\multiprocessing\\resource_sharer.py',
   'PYMODULE'),
  ('multiprocessing.process',
   'D:\\ProgramData\\anaconda3\\Lib\\multiprocessing\\process.py',
   'PYMODULE'),
  ('signal', 'D:\\ProgramData\\anaconda3\\Lib\\signal.py', 'PYMODULE'),
  ('selectors', 'D:\\ProgramData\\anaconda3\\Lib\\selectors.py', 'PYMODULE'),
  ('xmlrpc.client',
   'D:\\ProgramData\\anaconda3\\Lib\\xmlrpc\\client.py',
   'PYMODULE'),
  ('xmlrpc',
   'D:\\ProgramData\\anaconda3\\Lib\\xmlrpc\\__init__.py',
   'PYMODULE'),
  ('gzip', 'D:\\ProgramData\\anaconda3\\Lib\\gzip.py', 'PYMODULE'),
  ('argparse', 'D:\\ProgramData\\anaconda3\\Lib\\argparse.py', 'PYMODULE'),
  ('textwrap', 'D:\\ProgramData\\anaconda3\\Lib\\textwrap.py', 'PYMODULE'),
  ('shutil', 'D:\\ProgramData\\anaconda3\\Lib\\shutil.py', 'PYMODULE'),
  ('zipfile', 'D:\\ProgramData\\anaconda3\\Lib\\zipfile.py', 'PYMODULE'),
  ('py_compile', 'D:\\ProgramData\\anaconda3\\Lib\\py_compile.py', 'PYMODULE'),
  ('importlib.machinery',
   'D:\\ProgramData\\anaconda3\\Lib\\importlib\\machinery.py',
   'PYMODULE'),
  ('importlib',
   'D:\\ProgramData\\anaconda3\\Lib\\importlib\\__init__.py',
   'PYMODULE'),
  ('importlib._bootstrap',
   'D:\\ProgramData\\anaconda3\\Lib\\importlib\\_bootstrap.py',
   'PYMODULE'),
  ('importlib._bootstrap_external',
   'D:\\ProgramData\\anaconda3\\Lib\\importlib\\_bootstrap_external.py',
   'PYMODULE'),
  ('importlib.metadata',
   'D:\\ProgramData\\anaconda3\\Lib\\importlib\\metadata\\__init__.py',
   'PYMODULE'),
  ('typing', 'D:\\ProgramData\\anaconda3\\Lib\\typing.py', 'PYMODULE'),
  ('importlib.abc',
   'D:\\ProgramData\\anaconda3\\Lib\\importlib\\abc.py',
   'PYMODULE'),
  ('importlib.resources.abc',
   'D:\\ProgramData\\anaconda3\\Lib\\importlib\\resources\\abc.py',
   'PYMODULE'),
  ('importlib.resources',
   'D:\\ProgramData\\anaconda3\\Lib\\importlib\\resources\\__init__.py',
   'PYMODULE'),
  ('importlib.resources._legacy',
   'D:\\ProgramData\\anaconda3\\Lib\\importlib\\resources\\_legacy.py',
   'PYMODULE'),
  ('importlib.resources._common',
   'D:\\ProgramData\\anaconda3\\Lib\\importlib\\resources\\_common.py',
   'PYMODULE'),
  ('importlib.resources._adapters',
   'D:\\ProgramData\\anaconda3\\Lib\\importlib\\resources\\_adapters.py',
   'PYMODULE'),
  ('importlib._abc',
   'D:\\ProgramData\\anaconda3\\Lib\\importlib\\_abc.py',
   'PYMODULE'),
  ('importlib.metadata._itertools',
   'D:\\ProgramData\\anaconda3\\Lib\\importlib\\metadata\\_itertools.py',
   'PYMODULE'),
  ('importlib.metadata._functools',
   'D:\\ProgramData\\anaconda3\\Lib\\importlib\\metadata\\_functools.py',
   'PYMODULE'),
  ('importlib.metadata._collections',
   'D:\\ProgramData\\anaconda3\\Lib\\importlib\\metadata\\_collections.py',
   'PYMODULE'),
  ('importlib.metadata._meta',
   'D:\\ProgramData\\anaconda3\\Lib\\importlib\\metadata\\_meta.py',
   'PYMODULE'),
  ('importlib.metadata._adapters',
   'D:\\ProgramData\\anaconda3\\Lib\\importlib\\metadata\\_adapters.py',
   'PYMODULE'),
  ('importlib.metadata._text',
   'D:\\ProgramData\\anaconda3\\Lib\\importlib\\metadata\\_text.py',
   'PYMODULE'),
  ('email.message',
   'D:\\ProgramData\\anaconda3\\Lib\\email\\message.py',
   'PYMODULE'),
  ('email.policy',
   'D:\\ProgramData\\anaconda3\\Lib\\email\\policy.py',
   'PYMODULE'),
  ('email.contentmanager',
   'D:\\ProgramData\\anaconda3\\Lib\\email\\contentmanager.py',
   'PYMODULE'),
  ('email.quoprimime',
   'D:\\ProgramData\\anaconda3\\Lib\\email\\quoprimime.py',
   'PYMODULE'),
  ('string', 'D:\\ProgramData\\anaconda3\\Lib\\string.py', 'PYMODULE'),
  ('email.headerregistry',
   'D:\\ProgramData\\anaconda3\\Lib\\email\\headerregistry.py',
   'PYMODULE'),
  ('email._header_value_parser',
   'D:\\ProgramData\\anaconda3\\Lib\\email\\_header_value_parser.py',
   'PYMODULE'),
  ('email.iterators',
   'D:\\ProgramData\\anaconda3\\Lib\\email\\iterators.py',
   'PYMODULE'),
  ('email.generator',
   'D:\\ProgramData\\anaconda3\\Lib\\email\\generator.py',
   'PYMODULE'),
  ('random', 'D:\\ProgramData\\anaconda3\\Lib\\random.py', 'PYMODULE'),
  ('statistics', 'D:\\ProgramData\\anaconda3\\Lib\\statistics.py', 'PYMODULE'),
  ('fractions', 'D:\\ProgramData\\anaconda3\\Lib\\fractions.py', 'PYMODULE'),
  ('numbers', 'D:\\ProgramData\\anaconda3\\Lib\\numbers.py', 'PYMODULE'),
  ('hashlib', 'D:\\ProgramData\\anaconda3\\Lib\\hashlib.py', 'PYMODULE'),
  ('bisect', 'D:\\ProgramData\\anaconda3\\Lib\\bisect.py', 'PYMODULE'),
  ('email._encoded_words',
   'D:\\ProgramData\\anaconda3\\Lib\\email\\_encoded_words.py',
   'PYMODULE'),
  ('email.charset',
   'D:\\ProgramData\\anaconda3\\Lib\\email\\charset.py',
   'PYMODULE'),
  ('email.encoders',
   'D:\\ProgramData\\anaconda3\\Lib\\email\\encoders.py',
   'PYMODULE'),
  ('email.base64mime',
   'D:\\ProgramData\\anaconda3\\Lib\\email\\base64mime.py',
   'PYMODULE'),
  ('email._policybase',
   'D:\\ProgramData\\anaconda3\\Lib\\email\\_policybase.py',
   'PYMODULE'),
  ('email.header',
   'D:\\ProgramData\\anaconda3\\Lib\\email\\header.py',
   'PYMODULE'),
  ('email.errors',
   'D:\\ProgramData\\anaconda3\\Lib\\email\\errors.py',
   'PYMODULE'),
  ('email.utils',
   'D:\\ProgramData\\anaconda3\\Lib\\email\\utils.py',
   'PYMODULE'),
  ('email._parseaddr',
   'D:\\ProgramData\\anaconda3\\Lib\\email\\_parseaddr.py',
   'PYMODULE'),
  ('calendar', 'D:\\ProgramData\\anaconda3\\Lib\\calendar.py', 'PYMODULE'),
  ('quopri', 'D:\\ProgramData\\anaconda3\\Lib\\quopri.py', 'PYMODULE'),
  ('getopt', 'D:\\ProgramData\\anaconda3\\Lib\\getopt.py', 'PYMODULE'),
  ('email', 'D:\\ProgramData\\anaconda3\\Lib\\email\\__init__.py', 'PYMODULE'),
  ('email.parser',
   'D:\\ProgramData\\anaconda3\\Lib\\email\\parser.py',
   'PYMODULE'),
  ('email.feedparser',
   'D:\\ProgramData\\anaconda3\\Lib\\email\\feedparser.py',
   'PYMODULE'),
  ('csv', 'D:\\ProgramData\\anaconda3\\Lib\\csv.py', 'PYMODULE'),
  ('importlib.readers',
   'D:\\ProgramData\\anaconda3\\Lib\\importlib\\readers.py',
   'PYMODULE'),
  ('importlib.resources.readers',
   'D:\\ProgramData\\anaconda3\\Lib\\importlib\\resources\\readers.py',
   'PYMODULE'),
  ('importlib.resources._itertools',
   'D:\\ProgramData\\anaconda3\\Lib\\importlib\\resources\\_itertools.py',
   'PYMODULE'),
  ('contextlib', 'D:\\ProgramData\\anaconda3\\Lib\\contextlib.py', 'PYMODULE'),
  ('importlib.util',
   'D:\\ProgramData\\anaconda3\\Lib\\importlib\\util.py',
   'PYMODULE'),
  ('tarfile', 'D:\\ProgramData\\anaconda3\\Lib\\tarfile.py', 'PYMODULE'),
  ('lzma', 'D:\\ProgramData\\anaconda3\\Lib\\lzma.py', 'PYMODULE'),
  ('bz2', 'D:\\ProgramData\\anaconda3\\Lib\\bz2.py', 'PYMODULE'),
  ('copy', 'D:\\ProgramData\\anaconda3\\Lib\\copy.py', 'PYMODULE'),
  ('gettext', 'D:\\ProgramData\\anaconda3\\Lib\\gettext.py', 'PYMODULE'),
  ('_compression',
   'D:\\ProgramData\\anaconda3\\Lib\\_compression.py',
   'PYMODULE'),
  ('xml.parsers.expat',
   'D:\\ProgramData\\anaconda3\\Lib\\xml\\parsers\\expat.py',
   'PYMODULE'),
  ('xml.parsers',
   'D:\\ProgramData\\anaconda3\\Lib\\xml\\parsers\\__init__.py',
   'PYMODULE'),
  ('xml', 'D:\\ProgramData\\anaconda3\\Lib\\xml\\__init__.py', 'PYMODULE'),
  ('xml.sax.expatreader',
   'D:\\ProgramData\\anaconda3\\Lib\\xml\\sax\\expatreader.py',
   'PYMODULE'),
  ('xml.sax.saxutils',
   'D:\\ProgramData\\anaconda3\\Lib\\xml\\sax\\saxutils.py',
   'PYMODULE'),
  ('xml.sax',
   'D:\\ProgramData\\anaconda3\\Lib\\xml\\sax\\__init__.py',
   'PYMODULE'),
  ('xml.sax.handler',
   'D:\\ProgramData\\anaconda3\\Lib\\xml\\sax\\handler.py',
   'PYMODULE'),
  ('xml.sax._exceptions',
   'D:\\ProgramData\\anaconda3\\Lib\\xml\\sax\\_exceptions.py',
   'PYMODULE'),
  ('xml.sax.xmlreader',
   'D:\\ProgramData\\anaconda3\\Lib\\xml\\sax\\xmlreader.py',
   'PYMODULE'),
  ('http.client',
   'D:\\ProgramData\\anaconda3\\Lib\\http\\client.py',
   'PYMODULE'),
  ('ssl', 'D:\\ProgramData\\anaconda3\\Lib\\ssl.py', 'PYMODULE'),
  ('http', 'D:\\ProgramData\\anaconda3\\Lib\\http\\__init__.py', 'PYMODULE'),
  ('decimal', 'D:\\ProgramData\\anaconda3\\Lib\\decimal.py', 'PYMODULE'),
  ('_pydecimal', 'D:\\ProgramData\\anaconda3\\Lib\\_pydecimal.py', 'PYMODULE'),
  ('contextvars',
   'D:\\ProgramData\\anaconda3\\Lib\\contextvars.py',
   'PYMODULE'),
  ('datetime', 'D:\\ProgramData\\anaconda3\\Lib\\datetime.py', 'PYMODULE'),
  ('_strptime', 'D:\\ProgramData\\anaconda3\\Lib\\_strptime.py', 'PYMODULE'),
  ('base64', 'D:\\ProgramData\\anaconda3\\Lib\\base64.py', 'PYMODULE'),
  ('hmac', 'D:\\ProgramData\\anaconda3\\Lib\\hmac.py', 'PYMODULE'),
  ('tempfile', 'D:\\ProgramData\\anaconda3\\Lib\\tempfile.py', 'PYMODULE'),
  ('struct', 'D:\\ProgramData\\anaconda3\\Lib\\struct.py', 'PYMODULE'),
  ('socket', 'D:\\ProgramData\\anaconda3\\Lib\\socket.py', 'PYMODULE'),
  ('multiprocessing.util',
   'D:\\ProgramData\\anaconda3\\Lib\\multiprocessing\\util.py',
   'PYMODULE'),
  ('multiprocessing.resource_tracker',
   'D:\\ProgramData\\anaconda3\\Lib\\multiprocessing\\resource_tracker.py',
   'PYMODULE'),
  ('multiprocessing.popen_fork',
   'D:\\ProgramData\\anaconda3\\Lib\\multiprocessing\\popen_fork.py',
   'PYMODULE'),
  ('multiprocessing.forkserver',
   'D:\\ProgramData\\anaconda3\\Lib\\multiprocessing\\forkserver.py',
   'PYMODULE'),
  ('multiprocessing.context',
   'D:\\ProgramData\\anaconda3\\Lib\\multiprocessing\\context.py',
   'PYMODULE'),
  ('multiprocessing.sharedctypes',
   'D:\\ProgramData\\anaconda3\\Lib\\multiprocessing\\sharedctypes.py',
   'PYMODULE'),
  ('multiprocessing.heap',
   'D:\\ProgramData\\anaconda3\\Lib\\multiprocessing\\heap.py',
   'PYMODULE'),
  ('ctypes',
   'D:\\ProgramData\\anaconda3\\Lib\\ctypes\\__init__.py',
   'PYMODULE'),
  ('ctypes._endian',
   'D:\\ProgramData\\anaconda3\\Lib\\ctypes\\_endian.py',
   'PYMODULE'),
  ('multiprocessing.pool',
   'D:\\ProgramData\\anaconda3\\Lib\\multiprocessing\\pool.py',
   'PYMODULE'),
  ('multiprocessing.dummy',
   'D:\\ProgramData\\anaconda3\\Lib\\multiprocessing\\dummy\\__init__.py',
   'PYMODULE'),
  ('multiprocessing.dummy.connection',
   'D:\\ProgramData\\anaconda3\\Lib\\multiprocessing\\dummy\\connection.py',
   'PYMODULE'),
  ('queue', 'D:\\ProgramData\\anaconda3\\Lib\\queue.py', 'PYMODULE'),
  ('multiprocessing.queues',
   'D:\\ProgramData\\anaconda3\\Lib\\multiprocessing\\queues.py',
   'PYMODULE'),
  ('multiprocessing.synchronize',
   'D:\\ProgramData\\anaconda3\\Lib\\multiprocessing\\synchronize.py',
   'PYMODULE'),
  ('multiprocessing.managers',
   'D:\\ProgramData\\anaconda3\\Lib\\multiprocessing\\managers.py',
   'PYMODULE'),
  ('multiprocessing.shared_memory',
   'D:\\ProgramData\\anaconda3\\Lib\\multiprocessing\\shared_memory.py',
   'PYMODULE'),
  ('secrets', 'D:\\ProgramData\\anaconda3\\Lib\\secrets.py', 'PYMODULE'),
  ('multiprocessing.reduction',
   'D:\\ProgramData\\anaconda3\\Lib\\multiprocessing\\reduction.py',
   'PYMODULE'),
  ('pickle', 'D:\\ProgramData\\anaconda3\\Lib\\pickle.py', 'PYMODULE'),
  ('pprint', 'D:\\ProgramData\\anaconda3\\Lib\\pprint.py', 'PYMODULE'),
  ('dataclasses',
   'D:\\ProgramData\\anaconda3\\Lib\\dataclasses.py',
   'PYMODULE'),
  ('inspect', 'D:\\ProgramData\\anaconda3\\Lib\\inspect.py', 'PYMODULE'),
  ('dis', 'D:\\ProgramData\\anaconda3\\Lib\\dis.py', 'PYMODULE'),
  ('opcode', 'D:\\ProgramData\\anaconda3\\Lib\\opcode.py', 'PYMODULE'),
  ('ast', 'D:\\ProgramData\\anaconda3\\Lib\\ast.py', 'PYMODULE'),
  ('_compat_pickle',
   'D:\\ProgramData\\anaconda3\\Lib\\_compat_pickle.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_posix',
   'D:\\ProgramData\\anaconda3\\Lib\\multiprocessing\\popen_spawn_posix.py',
   'PYMODULE'),
  ('multiprocessing.popen_spawn_win32',
   'D:\\ProgramData\\anaconda3\\Lib\\multiprocessing\\popen_spawn_win32.py',
   'PYMODULE'),
  ('subprocess', 'D:\\ProgramData\\anaconda3\\Lib\\subprocess.py', 'PYMODULE'),
  ('multiprocessing.spawn',
   'D:\\ProgramData\\anaconda3\\Lib\\multiprocessing\\spawn.py',
   'PYMODULE'),
  ('runpy', 'D:\\ProgramData\\anaconda3\\Lib\\runpy.py', 'PYMODULE'),
  ('pkgutil', 'D:\\ProgramData\\anaconda3\\Lib\\pkgutil.py', 'PYMODULE'),
  ('zipimport', 'D:\\ProgramData\\anaconda3\\Lib\\zipimport.py', 'PYMODULE'),
  ('multiprocessing',
   'D:\\ProgramData\\anaconda3\\Lib\\multiprocessing\\__init__.py',
   'PYMODULE'),
  ('threading', 'D:\\ProgramData\\anaconda3\\Lib\\threading.py', 'PYMODULE'),
  ('_threading_local',
   'D:\\ProgramData\\anaconda3\\Lib\\_threading_local.py',
   'PYMODULE'),
  ('getpass', 'D:\\ProgramData\\anaconda3\\Lib\\getpass.py', 'PYMODULE'),
  ('nturl2path', 'D:\\ProgramData\\anaconda3\\Lib\\nturl2path.py', 'PYMODULE'),
  ('ftplib', 'D:\\ProgramData\\anaconda3\\Lib\\ftplib.py', 'PYMODULE'),
  ('netrc', 'D:\\ProgramData\\anaconda3\\Lib\\netrc.py', 'PYMODULE'),
  ('shlex', 'D:\\ProgramData\\anaconda3\\Lib\\shlex.py', 'PYMODULE'),
  ('mimetypes', 'D:\\ProgramData\\anaconda3\\Lib\\mimetypes.py', 'PYMODULE'),
  ('http.cookiejar',
   'D:\\ProgramData\\anaconda3\\Lib\\http\\cookiejar.py',
   'PYMODULE'),
  ('stringprep', 'D:\\ProgramData\\anaconda3\\Lib\\stringprep.py', 'PYMODULE'),
  ('tracemalloc',
   'D:\\ProgramData\\anaconda3\\Lib\\tracemalloc.py',
   'PYMODULE'),
  ('_py_abc', 'D:\\ProgramData\\anaconda3\\Lib\\_py_abc.py', 'PYMODULE'),
  ('bs4',
   'D:\\ProgramData\\anaconda3\\Lib\\site-packages\\bs4\\__init__.py',
   'PYMODULE'),
  ('bs4.element',
   'D:\\ProgramData\\anaconda3\\Lib\\site-packages\\bs4\\element.py',
   'PYMODULE'),
  ('bs4.formatter',
   'D:\\ProgramData\\anaconda3\\Lib\\site-packages\\bs4\\formatter.py',
   'PYMODULE'),
  ('bs4.css',
   'D:\\ProgramData\\anaconda3\\Lib\\site-packages\\bs4\\css.py',
   'PYMODULE'),
  ('soupsieve',
   'D:\\ProgramData\\anaconda3\\Lib\\site-packages\\soupsieve\\__init__.py',
   'PYMODULE'),
  ('soupsieve.css_parser',
   'D:\\ProgramData\\anaconda3\\Lib\\site-packages\\soupsieve\\css_parser.py',
   'PYMODULE'),
  ('soupsieve.css_match',
   'D:\\ProgramData\\anaconda3\\Lib\\site-packages\\soupsieve\\css_match.py',
   'PYMODULE'),
  ('soupsieve.css_types',
   'D:\\ProgramData\\anaconda3\\Lib\\site-packages\\soupsieve\\css_types.py',
   'PYMODULE'),
  ('soupsieve.pretty',
   'D:\\ProgramData\\anaconda3\\Lib\\site-packages\\soupsieve\\pretty.py',
   'PYMODULE'),
  ('soupsieve.util',
   'D:\\ProgramData\\anaconda3\\Lib\\site-packages\\soupsieve\\util.py',
   'PYMODULE'),
  ('soupsieve.__meta__',
   'D:\\ProgramData\\anaconda3\\Lib\\site-packages\\soupsieve\\__meta__.py',
   'PYMODULE'),
  ('__future__', 'D:\\ProgramData\\anaconda3\\Lib\\__future__.py', 'PYMODULE'),
  ('bs4.dammit',
   'D:\\ProgramData\\anaconda3\\Lib\\site-packages\\bs4\\dammit.py',
   'PYMODULE'),
  ('charset_normalizer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\charset_normalizer\\__init__.py',
   'PYMODULE'),
  ('charset_normalizer.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\charset_normalizer\\version.py',
   'PYMODULE'),
  ('charset_normalizer.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\charset_normalizer\\utils.py',
   'PYMODULE'),
  ('charset_normalizer.constant',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\charset_normalizer\\constant.py',
   'PYMODULE'),
  ('charset_normalizer.models',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\charset_normalizer\\models.py',
   'PYMODULE'),
  ('charset_normalizer.cd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\charset_normalizer\\cd.py',
   'PYMODULE'),
  ('json', 'D:\\ProgramData\\anaconda3\\Lib\\json\\__init__.py', 'PYMODULE'),
  ('json.encoder',
   'D:\\ProgramData\\anaconda3\\Lib\\json\\encoder.py',
   'PYMODULE'),
  ('json.decoder',
   'D:\\ProgramData\\anaconda3\\Lib\\json\\decoder.py',
   'PYMODULE'),
  ('json.scanner',
   'D:\\ProgramData\\anaconda3\\Lib\\json\\scanner.py',
   'PYMODULE'),
  ('charset_normalizer.legacy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\charset_normalizer\\legacy.py',
   'PYMODULE'),
  ('typing_extensions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\typing_extensions.py',
   'PYMODULE'),
  ('asyncio.coroutines',
   'D:\\ProgramData\\anaconda3\\Lib\\asyncio\\coroutines.py',
   'PYMODULE'),
  ('asyncio',
   'D:\\ProgramData\\anaconda3\\Lib\\asyncio\\__init__.py',
   'PYMODULE'),
  ('asyncio.unix_events',
   'D:\\ProgramData\\anaconda3\\Lib\\asyncio\\unix_events.py',
   'PYMODULE'),
  ('asyncio.log',
   'D:\\ProgramData\\anaconda3\\Lib\\asyncio\\log.py',
   'PYMODULE'),
  ('asyncio.windows_events',
   'D:\\ProgramData\\anaconda3\\Lib\\asyncio\\windows_events.py',
   'PYMODULE'),
  ('asyncio.windows_utils',
   'D:\\ProgramData\\anaconda3\\Lib\\asyncio\\windows_utils.py',
   'PYMODULE'),
  ('asyncio.selector_events',
   'D:\\ProgramData\\anaconda3\\Lib\\asyncio\\selector_events.py',
   'PYMODULE'),
  ('asyncio.proactor_events',
   'D:\\ProgramData\\anaconda3\\Lib\\asyncio\\proactor_events.py',
   'PYMODULE'),
  ('asyncio.base_subprocess',
   'D:\\ProgramData\\anaconda3\\Lib\\asyncio\\base_subprocess.py',
   'PYMODULE'),
  ('asyncio.threads',
   'D:\\ProgramData\\anaconda3\\Lib\\asyncio\\threads.py',
   'PYMODULE'),
  ('asyncio.timeouts',
   'D:\\ProgramData\\anaconda3\\Lib\\asyncio\\timeouts.py',
   'PYMODULE'),
  ('asyncio.taskgroups',
   'D:\\ProgramData\\anaconda3\\Lib\\asyncio\\taskgroups.py',
   'PYMODULE'),
  ('asyncio.subprocess',
   'D:\\ProgramData\\anaconda3\\Lib\\asyncio\\subprocess.py',
   'PYMODULE'),
  ('asyncio.streams',
   'D:\\ProgramData\\anaconda3\\Lib\\asyncio\\streams.py',
   'PYMODULE'),
  ('asyncio.queues',
   'D:\\ProgramData\\anaconda3\\Lib\\asyncio\\queues.py',
   'PYMODULE'),
  ('asyncio.runners',
   'D:\\ProgramData\\anaconda3\\Lib\\asyncio\\runners.py',
   'PYMODULE'),
  ('asyncio.base_events',
   'D:\\ProgramData\\anaconda3\\Lib\\asyncio\\base_events.py',
   'PYMODULE'),
  ('concurrent.futures',
   'D:\\ProgramData\\anaconda3\\Lib\\concurrent\\futures\\__init__.py',
   'PYMODULE'),
  ('concurrent.futures.thread',
   'D:\\ProgramData\\anaconda3\\Lib\\concurrent\\futures\\thread.py',
   'PYMODULE'),
  ('concurrent.futures.process',
   'D:\\ProgramData\\anaconda3\\Lib\\concurrent\\futures\\process.py',
   'PYMODULE'),
  ('concurrent.futures._base',
   'D:\\ProgramData\\anaconda3\\Lib\\concurrent\\futures\\_base.py',
   'PYMODULE'),
  ('concurrent',
   'D:\\ProgramData\\anaconda3\\Lib\\concurrent\\__init__.py',
   'PYMODULE'),
  ('asyncio.trsock',
   'D:\\ProgramData\\anaconda3\\Lib\\asyncio\\trsock.py',
   'PYMODULE'),
  ('asyncio.staggered',
   'D:\\ProgramData\\anaconda3\\Lib\\asyncio\\staggered.py',
   'PYMODULE'),
  ('asyncio.tasks',
   'D:\\ProgramData\\anaconda3\\Lib\\asyncio\\tasks.py',
   'PYMODULE'),
  ('asyncio.locks',
   'D:\\ProgramData\\anaconda3\\Lib\\asyncio\\locks.py',
   'PYMODULE'),
  ('asyncio.base_tasks',
   'D:\\ProgramData\\anaconda3\\Lib\\asyncio\\base_tasks.py',
   'PYMODULE'),
  ('asyncio.mixins',
   'D:\\ProgramData\\anaconda3\\Lib\\asyncio\\mixins.py',
   'PYMODULE'),
  ('asyncio.sslproto',
   'D:\\ProgramData\\anaconda3\\Lib\\asyncio\\sslproto.py',
   'PYMODULE'),
  ('asyncio.transports',
   'D:\\ProgramData\\anaconda3\\Lib\\asyncio\\transports.py',
   'PYMODULE'),
  ('asyncio.protocols',
   'D:\\ProgramData\\anaconda3\\Lib\\asyncio\\protocols.py',
   'PYMODULE'),
  ('asyncio.futures',
   'D:\\ProgramData\\anaconda3\\Lib\\asyncio\\futures.py',
   'PYMODULE'),
  ('asyncio.base_futures',
   'D:\\ProgramData\\anaconda3\\Lib\\asyncio\\base_futures.py',
   'PYMODULE'),
  ('asyncio.exceptions',
   'D:\\ProgramData\\anaconda3\\Lib\\asyncio\\exceptions.py',
   'PYMODULE'),
  ('asyncio.events',
   'D:\\ProgramData\\anaconda3\\Lib\\asyncio\\events.py',
   'PYMODULE'),
  ('asyncio.format_helpers',
   'D:\\ProgramData\\anaconda3\\Lib\\asyncio\\format_helpers.py',
   'PYMODULE'),
  ('asyncio.constants',
   'D:\\ProgramData\\anaconda3\\Lib\\asyncio\\constants.py',
   'PYMODULE'),
  ('charset_normalizer.api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\charset_normalizer\\api.py',
   'PYMODULE'),
  ('chardet',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\chardet\\__init__.py',
   'PYMODULE'),
  ('chardet.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\chardet\\version.py',
   'PYMODULE'),
  ('chardet.universaldetector',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\chardet\\universaldetector.py',
   'PYMODULE'),
  ('chardet.utf1632prober',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\chardet\\utf1632prober.py',
   'PYMODULE'),
  ('chardet.sbcsgroupprober',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\chardet\\sbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.sbcharsetprober',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\chardet\\sbcharsetprober.py',
   'PYMODULE'),
  ('chardet.langturkishmodel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\chardet\\langturkishmodel.py',
   'PYMODULE'),
  ('chardet.langthaimodel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\chardet\\langthaimodel.py',
   'PYMODULE'),
  ('chardet.langrussianmodel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\chardet\\langrussianmodel.py',
   'PYMODULE'),
  ('chardet.langhebrewmodel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\chardet\\langhebrewmodel.py',
   'PYMODULE'),
  ('chardet.langgreekmodel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\chardet\\langgreekmodel.py',
   'PYMODULE'),
  ('chardet.langbulgarianmodel',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\chardet\\langbulgarianmodel.py',
   'PYMODULE'),
  ('chardet.hebrewprober',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\chardet\\hebrewprober.py',
   'PYMODULE'),
  ('chardet.mbcsgroupprober',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\chardet\\mbcsgroupprober.py',
   'PYMODULE'),
  ('chardet.utf8prober',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\chardet\\utf8prober.py',
   'PYMODULE'),
  ('chardet.mbcssm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\chardet\\mbcssm.py',
   'PYMODULE'),
  ('chardet.codingstatemachinedict',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\chardet\\codingstatemachinedict.py',
   'PYMODULE'),
  ('chardet.codingstatemachine',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\chardet\\codingstatemachine.py',
   'PYMODULE'),
  ('chardet.sjisprober',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\chardet\\sjisprober.py',
   'PYMODULE'),
  ('chardet.mbcharsetprober',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\chardet\\mbcharsetprober.py',
   'PYMODULE'),
  ('chardet.jpcntx',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\chardet\\jpcntx.py',
   'PYMODULE'),
  ('chardet.chardistribution',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\chardet\\chardistribution.py',
   'PYMODULE'),
  ('chardet.johabfreq',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\chardet\\johabfreq.py',
   'PYMODULE'),
  ('chardet.jisfreq',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\chardet\\jisfreq.py',
   'PYMODULE'),
  ('chardet.gb2312freq',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\chardet\\gb2312freq.py',
   'PYMODULE'),
  ('chardet.euctwfreq',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\chardet\\euctwfreq.py',
   'PYMODULE'),
  ('chardet.euckrfreq',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\chardet\\euckrfreq.py',
   'PYMODULE'),
  ('chardet.big5freq',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\chardet\\big5freq.py',
   'PYMODULE'),
  ('chardet.johabprober',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\chardet\\johabprober.py',
   'PYMODULE'),
  ('chardet.gb2312prober',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\chardet\\gb2312prober.py',
   'PYMODULE'),
  ('chardet.euctwprober',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\chardet\\euctwprober.py',
   'PYMODULE'),
  ('chardet.euckrprober',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\chardet\\euckrprober.py',
   'PYMODULE'),
  ('chardet.eucjpprober',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\chardet\\eucjpprober.py',
   'PYMODULE'),
  ('chardet.cp949prober',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\chardet\\cp949prober.py',
   'PYMODULE'),
  ('chardet.big5prober',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\chardet\\big5prober.py',
   'PYMODULE'),
  ('chardet.macromanprober',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\chardet\\macromanprober.py',
   'PYMODULE'),
  ('chardet.latin1prober',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\chardet\\latin1prober.py',
   'PYMODULE'),
  ('chardet.escprober',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\chardet\\escprober.py',
   'PYMODULE'),
  ('chardet.escsm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\chardet\\escsm.py',
   'PYMODULE'),
  ('chardet.resultdict',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\chardet\\resultdict.py',
   'PYMODULE'),
  ('chardet.enums',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\chardet\\enums.py',
   'PYMODULE'),
  ('chardet.charsetprober',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\chardet\\charsetprober.py',
   'PYMODULE'),
  ('chardet.charsetgroupprober',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\chardet\\charsetgroupprober.py',
   'PYMODULE'),
  ('html.entities',
   'D:\\ProgramData\\anaconda3\\Lib\\html\\entities.py',
   'PYMODULE'),
  ('html', 'D:\\ProgramData\\anaconda3\\Lib\\html\\__init__.py', 'PYMODULE'),
  ('bs4.builder',
   'D:\\ProgramData\\anaconda3\\Lib\\site-packages\\bs4\\builder\\__init__.py',
   'PYMODULE'),
  ('bs4.builder._lxml',
   'D:\\ProgramData\\anaconda3\\Lib\\site-packages\\bs4\\builder\\_lxml.py',
   'PYMODULE'),
  ('lxml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\lxml\\__init__.py',
   'PYMODULE'),
  ('lxml.usedoctest',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\lxml\\usedoctest.py',
   'PYMODULE'),
  ('lxml.pyclasslookup',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\lxml\\pyclasslookup.py',
   'PYMODULE'),
  ('lxml.isoschematron',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\lxml\\isoschematron\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxslt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\lxml\\includes\\libxslt\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libxml',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\lxml\\includes\\libxml\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.libexslt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\lxml\\includes\\libexslt\\__init__.py',
   'PYMODULE'),
  ('lxml.includes.extlibs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\lxml\\includes\\extlibs\\__init__.py',
   'PYMODULE'),
  ('lxml.includes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\lxml\\includes\\__init__.py',
   'PYMODULE'),
  ('lxml.html.usedoctest',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\lxml\\html\\usedoctest.py',
   'PYMODULE'),
  ('lxml.html.soupparser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\lxml\\html\\soupparser.py',
   'PYMODULE'),
  ('lxml.html.html5parser',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\lxml\\html\\html5parser.py',
   'PYMODULE'),
  ('lxml.html.formfill',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\lxml\\html\\formfill.py',
   'PYMODULE'),
  ('lxml.html.defs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\lxml\\html\\defs.py',
   'PYMODULE'),
  ('lxml.html.clean',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\lxml\\html\\clean.py',
   'PYMODULE'),
  ('lxml.html.builder',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\lxml\\html\\builder.py',
   'PYMODULE'),
  ('lxml.html._setmixin',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\lxml\\html\\_setmixin.py',
   'PYMODULE'),
  ('lxml.html._html5builder',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\lxml\\html\\_html5builder.py',
   'PYMODULE'),
  ('lxml.html._diffcommand',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\lxml\\html\\_diffcommand.py',
   'PYMODULE'),
  ('optparse', 'D:\\ProgramData\\anaconda3\\Lib\\optparse.py', 'PYMODULE'),
  ('lxml.html.ElementSoup',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\lxml\\html\\ElementSoup.py',
   'PYMODULE'),
  ('lxml.html',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\lxml\\html\\__init__.py',
   'PYMODULE'),
  ('webbrowser', 'D:\\ProgramData\\anaconda3\\Lib\\webbrowser.py', 'PYMODULE'),
  ('glob', 'D:\\ProgramData\\anaconda3\\Lib\\glob.py', 'PYMODULE'),
  ('lxml.doctestcompare',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\lxml\\doctestcompare.py',
   'PYMODULE'),
  ('cgi', 'D:\\ProgramData\\anaconda3\\Lib\\cgi.py', 'PYMODULE'),
  ('doctest', 'D:\\ProgramData\\anaconda3\\Lib\\doctest.py', 'PYMODULE'),
  ('unittest',
   'D:\\ProgramData\\anaconda3\\Lib\\unittest\\__init__.py',
   'PYMODULE'),
  ('unittest.async_case',
   'D:\\ProgramData\\anaconda3\\Lib\\unittest\\async_case.py',
   'PYMODULE'),
  ('unittest.signals',
   'D:\\ProgramData\\anaconda3\\Lib\\unittest\\signals.py',
   'PYMODULE'),
  ('unittest.main',
   'D:\\ProgramData\\anaconda3\\Lib\\unittest\\main.py',
   'PYMODULE'),
  ('unittest.runner',
   'D:\\ProgramData\\anaconda3\\Lib\\unittest\\runner.py',
   'PYMODULE'),
  ('unittest.loader',
   'D:\\ProgramData\\anaconda3\\Lib\\unittest\\loader.py',
   'PYMODULE'),
  ('unittest.suite',
   'D:\\ProgramData\\anaconda3\\Lib\\unittest\\suite.py',
   'PYMODULE'),
  ('unittest.case',
   'D:\\ProgramData\\anaconda3\\Lib\\unittest\\case.py',
   'PYMODULE'),
  ('unittest._log',
   'D:\\ProgramData\\anaconda3\\Lib\\unittest\\_log.py',
   'PYMODULE'),
  ('unittest.result',
   'D:\\ProgramData\\anaconda3\\Lib\\unittest\\result.py',
   'PYMODULE'),
  ('unittest.util',
   'D:\\ProgramData\\anaconda3\\Lib\\unittest\\util.py',
   'PYMODULE'),
  ('pdb', 'D:\\ProgramData\\anaconda3\\Lib\\pdb.py', 'PYMODULE'),
  ('pydoc', 'D:\\ProgramData\\anaconda3\\Lib\\pydoc.py', 'PYMODULE'),
  ('http.server',
   'D:\\ProgramData\\anaconda3\\Lib\\http\\server.py',
   'PYMODULE'),
  ('socketserver',
   'D:\\ProgramData\\anaconda3\\Lib\\socketserver.py',
   'PYMODULE'),
  ('pydoc_data.topics',
   'D:\\ProgramData\\anaconda3\\Lib\\pydoc_data\\topics.py',
   'PYMODULE'),
  ('pydoc_data',
   'D:\\ProgramData\\anaconda3\\Lib\\pydoc_data\\__init__.py',
   'PYMODULE'),
  ('tty', 'D:\\ProgramData\\anaconda3\\Lib\\tty.py', 'PYMODULE'),
  ('sysconfig', 'D:\\ProgramData\\anaconda3\\Lib\\sysconfig.py', 'PYMODULE'),
  ('_aix_support',
   'D:\\ProgramData\\anaconda3\\Lib\\_aix_support.py',
   'PYMODULE'),
  ('_bootsubprocess',
   'D:\\ProgramData\\anaconda3\\Lib\\_bootsubprocess.py',
   'PYMODULE'),
  ('platform', 'D:\\ProgramData\\anaconda3\\Lib\\platform.py', 'PYMODULE'),
  ('readline',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\readline.py',
   'PYMODULE'),
  ('pyreadline3.console',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pyreadline3\\console\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.console.console',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pyreadline3\\console\\console.py',
   'PYMODULE'),
  ('ctypes.wintypes',
   'D:\\ProgramData\\anaconda3\\Lib\\ctypes\\wintypes.py',
   'PYMODULE'),
  ('ctypes.util',
   'D:\\ProgramData\\anaconda3\\Lib\\ctypes\\util.py',
   'PYMODULE'),
  ('ctypes._aix',
   'D:\\ProgramData\\anaconda3\\Lib\\ctypes\\_aix.py',
   'PYMODULE'),
  ('ctypes.macholib.dyld',
   'D:\\ProgramData\\anaconda3\\Lib\\ctypes\\macholib\\dyld.py',
   'PYMODULE'),
  ('ctypes.macholib',
   'D:\\ProgramData\\anaconda3\\Lib\\ctypes\\macholib\\__init__.py',
   'PYMODULE'),
  ('ctypes.macholib.dylib',
   'D:\\ProgramData\\anaconda3\\Lib\\ctypes\\macholib\\dylib.py',
   'PYMODULE'),
  ('ctypes.macholib.framework',
   'D:\\ProgramData\\anaconda3\\Lib\\ctypes\\macholib\\framework.py',
   'PYMODULE'),
  ('pyreadline3.logger.log',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pyreadline3\\logger\\log.py',
   'PYMODULE'),
  ('pyreadline3.logger.logger',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pyreadline3\\logger\\logger.py',
   'PYMODULE'),
  ('pyreadline3.logger.null_handler',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pyreadline3\\logger\\null_handler.py',
   'PYMODULE'),
  ('pyreadline3.logger',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pyreadline3\\logger\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.logger.control',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pyreadline3\\logger\\control.py',
   'PYMODULE'),
  ('pyreadline3.logger.socket_stream',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pyreadline3\\logger\\socket_stream.py',
   'PYMODULE'),
  ('logging.handlers',
   'D:\\ProgramData\\anaconda3\\Lib\\logging\\handlers.py',
   'PYMODULE'),
  ('win32evtlogutil',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\win32\\lib\\win32evtlogutil.py',
   'PYMODULE'),
  ('winerror',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\win32\\lib\\winerror.py',
   'PYMODULE'),
  ('win32con',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\win32\\lib\\win32con.py',
   'PYMODULE'),
  ('smtplib', 'D:\\ProgramData\\anaconda3\\Lib\\smtplib.py', 'PYMODULE'),
  ('pyreadline3.keysyms',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pyreadline3\\keysyms\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.keysyms',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pyreadline3\\keysyms\\keysyms.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.common',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pyreadline3\\keysyms\\common.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.ironpython_keysyms',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pyreadline3\\keysyms\\ironpython_keysyms.py',
   'PYMODULE'),
  ('pyreadline3.keysyms.winconstants',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pyreadline3\\keysyms\\winconstants.py',
   'PYMODULE'),
  ('pyreadline3.console.ansi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pyreadline3\\console\\ansi.py',
   'PYMODULE'),
  ('pyreadline3.unicode_helper',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pyreadline3\\unicode_helper.py',
   'PYMODULE'),
  ('pyreadline3.console.event',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pyreadline3\\console\\event.py',
   'PYMODULE'),
  ('pyreadline3.console.ironpython_console',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pyreadline3\\console\\ironpython_console.py',
   'PYMODULE'),
  ('pyreadline3',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pyreadline3\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.modes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pyreadline3\\modes\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.modes.vi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pyreadline3\\modes\\vi.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.lineobj',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pyreadline3\\lineeditor\\lineobj.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.wordmatcher',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pyreadline3\\lineeditor\\wordmatcher.py',
   'PYMODULE'),
  ('pyreadline3.modes.notemacs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pyreadline3\\modes\\notemacs.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor.history',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pyreadline3\\lineeditor\\history.py',
   'PYMODULE'),
  ('pyreadline3.modes.emacs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pyreadline3\\modes\\emacs.py',
   'PYMODULE'),
  ('pyreadline3.modes.basemode',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pyreadline3\\modes\\basemode.py',
   'PYMODULE'),
  ('pyreadline3.error',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pyreadline3\\error.py',
   'PYMODULE'),
  ('pyreadline3.lineeditor',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pyreadline3\\lineeditor\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.clipboard',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pyreadline3\\clipboard\\__init__.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.get_clipboard_text_and_convert',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pyreadline3\\clipboard\\get_clipboard_text_and_convert.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pyreadline3\\clipboard\\api.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.win32_clipboard',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pyreadline3\\clipboard\\win32_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.no_clipboard',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pyreadline3\\clipboard\\no_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.clipboard.ironpython_clipboard',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pyreadline3\\clipboard\\ironpython_clipboard.py',
   'PYMODULE'),
  ('pyreadline3.py3k_compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pyreadline3\\py3k_compat.py',
   'PYMODULE'),
  ('pyreadline3.rlmain',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\pyreadline3\\rlmain.py',
   'PYMODULE'),
  ('code', 'D:\\ProgramData\\anaconda3\\Lib\\code.py', 'PYMODULE'),
  ('codeop', 'D:\\ProgramData\\anaconda3\\Lib\\codeop.py', 'PYMODULE'),
  ('bdb', 'D:\\ProgramData\\anaconda3\\Lib\\bdb.py', 'PYMODULE'),
  ('cmd', 'D:\\ProgramData\\anaconda3\\Lib\\cmd.py', 'PYMODULE'),
  ('difflib', 'D:\\ProgramData\\anaconda3\\Lib\\difflib.py', 'PYMODULE'),
  ('lxml.cssselect',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\lxml\\cssselect.py',
   'PYMODULE'),
  ('cssselect',
   'D:\\ProgramData\\anaconda3\\Lib\\site-packages\\cssselect\\__init__.py',
   'PYMODULE'),
  ('cssselect.xpath',
   'D:\\ProgramData\\anaconda3\\Lib\\site-packages\\cssselect\\xpath.py',
   'PYMODULE'),
  ('cssselect.parser',
   'D:\\ProgramData\\anaconda3\\Lib\\site-packages\\cssselect\\parser.py',
   'PYMODULE'),
  ('lxml.ElementInclude',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\lxml\\ElementInclude.py',
   'PYMODULE'),
  ('bs4.builder._html5lib',
   'D:\\ProgramData\\anaconda3\\Lib\\site-packages\\bs4\\builder\\_html5lib.py',
   'PYMODULE'),
  ('bs4.builder._htmlparser',
   'D:\\ProgramData\\anaconda3\\Lib\\site-packages\\bs4\\builder\\_htmlparser.py',
   'PYMODULE'),
  ('html.parser',
   'D:\\ProgramData\\anaconda3\\Lib\\html\\parser.py',
   'PYMODULE'),
  ('_markupbase',
   'D:\\ProgramData\\anaconda3\\Lib\\_markupbase.py',
   'PYMODULE'),
  ('requests',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\requests\\__init__.py',
   'PYMODULE'),
  ('requests.status_codes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\requests\\status_codes.py',
   'PYMODULE'),
  ('requests.structures',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\requests\\structures.py',
   'PYMODULE'),
  ('requests.compat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\requests\\compat.py',
   'PYMODULE'),
  ('http.cookies',
   'D:\\ProgramData\\anaconda3\\Lib\\http\\cookies.py',
   'PYMODULE'),
  ('requests.models',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\requests\\models.py',
   'PYMODULE'),
  ('idna',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\idna\\__init__.py',
   'PYMODULE'),
  ('idna.package_data',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\idna\\package_data.py',
   'PYMODULE'),
  ('idna.intranges',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\idna\\intranges.py',
   'PYMODULE'),
  ('idna.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\idna\\core.py',
   'PYMODULE'),
  ('idna.uts46data',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\idna\\uts46data.py',
   'PYMODULE'),
  ('idna.idnadata',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\idna\\idnadata.py',
   'PYMODULE'),
  ('requests.hooks',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\requests\\hooks.py',
   'PYMODULE'),
  ('requests.cookies',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\requests\\cookies.py',
   'PYMODULE'),
  ('requests.auth',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\requests\\auth.py',
   'PYMODULE'),
  ('requests._internal_utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\requests\\_internal_utils.py',
   'PYMODULE'),
  ('urllib3.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\urllib3\\util\\__init__.py',
   'PYMODULE'),
  ('urllib3.util.wait',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\urllib3\\util\\wait.py',
   'PYMODULE'),
  ('urllib3.util.url',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\urllib3\\util\\url.py',
   'PYMODULE'),
  ('urllib3.util.util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\urllib3\\util\\util.py',
   'PYMODULE'),
  ('urllib3.util.timeout',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\urllib3\\util\\timeout.py',
   'PYMODULE'),
  ('urllib3.util.ssl_',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\urllib3\\util\\ssl_.py',
   'PYMODULE'),
  ('urllib3.util.ssltransport',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\urllib3\\util\\ssltransport.py',
   'PYMODULE'),
  ('urllib3.util.retry',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\urllib3\\util\\retry.py',
   'PYMODULE'),
  ('urllib3.response',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\urllib3\\response.py',
   'PYMODULE'),
  ('zstandard',
   'D:\\ProgramData\\anaconda3\\Lib\\site-packages\\zstandard\\__init__.py',
   'PYMODULE'),
  ('zstandard.backend_cffi',
   'D:\\ProgramData\\anaconda3\\Lib\\site-packages\\zstandard\\backend_cffi.py',
   'PYMODULE'),
  ('urllib3.connection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\urllib3\\connection.py',
   'PYMODULE'),
  ('urllib3.util.ssl_match_hostname',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\urllib3\\util\\ssl_match_hostname.py',
   'PYMODULE'),
  ('urllib3._version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\urllib3\\_version.py',
   'PYMODULE'),
  ('urllib3.http2.probe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\urllib3\\http2\\probe.py',
   'PYMODULE'),
  ('urllib3.http2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\urllib3\\http2\\__init__.py',
   'PYMODULE'),
  ('urllib3.http2.connection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\urllib3\\http2\\connection.py',
   'PYMODULE'),
  ('h2.events',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\h2\\events.py',
   'PYMODULE'),
  ('h2',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\h2\\__init__.py',
   'PYMODULE'),
  ('h2.errors',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\h2\\errors.py',
   'PYMODULE'),
  ('hyperframe.frame',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\hyperframe\\frame.py',
   'PYMODULE'),
  ('hyperframe',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\hyperframe\\__init__.py',
   'PYMODULE'),
  ('hyperframe.flags',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\hyperframe\\flags.py',
   'PYMODULE'),
  ('hyperframe.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\hyperframe\\exceptions.py',
   'PYMODULE'),
  ('hpack',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\hpack\\__init__.py',
   'PYMODULE'),
  ('hpack.struct',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\hpack\\struct.py',
   'PYMODULE'),
  ('hpack.hpack',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\hpack\\hpack.py',
   'PYMODULE'),
  ('hpack.table',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\hpack\\table.py',
   'PYMODULE'),
  ('hpack.huffman_table',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\hpack\\huffman_table.py',
   'PYMODULE'),
  ('hpack.huffman_constants',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\hpack\\huffman_constants.py',
   'PYMODULE'),
  ('hpack.huffman',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\hpack\\huffman.py',
   'PYMODULE'),
  ('hpack.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\hpack\\exceptions.py',
   'PYMODULE'),
  ('h2.settings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\h2\\settings.py',
   'PYMODULE'),
  ('h2.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\h2\\exceptions.py',
   'PYMODULE'),
  ('h2.connection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\h2\\connection.py',
   'PYMODULE'),
  ('h2.windows',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\h2\\windows.py',
   'PYMODULE'),
  ('h2.utilities',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\h2\\utilities.py',
   'PYMODULE'),
  ('h2.stream',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\h2\\stream.py',
   'PYMODULE'),
  ('h2.frame_buffer',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\h2\\frame_buffer.py',
   'PYMODULE'),
  ('h2.config',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\h2\\config.py',
   'PYMODULE'),
  ('urllib3._collections',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\urllib3\\_collections.py',
   'PYMODULE'),
  ('brotli',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\brotli.py',
   'PYMODULE'),
  ('urllib3._base_connection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\urllib3\\_base_connection.py',
   'PYMODULE'),
  ('urllib3.connectionpool',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\urllib3\\connectionpool.py',
   'PYMODULE'),
  ('urllib3.util.proxy',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\urllib3\\util\\proxy.py',
   'PYMODULE'),
  ('urllib3._request_methods',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\urllib3\\_request_methods.py',
   'PYMODULE'),
  ('urllib3.util.response',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\urllib3\\util\\response.py',
   'PYMODULE'),
  ('urllib3.util.request',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\urllib3\\util\\request.py',
   'PYMODULE'),
  ('urllib3.util.connection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\urllib3\\util\\connection.py',
   'PYMODULE'),
  ('urllib3.filepost',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\urllib3\\filepost.py',
   'PYMODULE'),
  ('urllib3.fields',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\urllib3\\fields.py',
   'PYMODULE'),
  ('requests.api',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\requests\\api.py',
   'PYMODULE'),
  ('requests.sessions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\requests\\sessions.py',
   'PYMODULE'),
  ('requests.adapters',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\requests\\adapters.py',
   'PYMODULE'),
  ('urllib3.contrib.socks',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\urllib3\\contrib\\socks.py',
   'PYMODULE'),
  ('socks',
   'D:\\ProgramData\\anaconda3\\Lib\\site-packages\\socks.py',
   'PYMODULE'),
  ('win_inet_pton',
   'D:\\ProgramData\\anaconda3\\Lib\\site-packages\\win_inet_pton.py',
   'PYMODULE'),
  ('urllib3.poolmanager',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\urllib3\\poolmanager.py',
   'PYMODULE'),
  ('requests.__version__',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\requests\\__version__.py',
   'PYMODULE'),
  ('requests.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\requests\\utils.py',
   'PYMODULE'),
  ('requests.certs',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\requests\\certs.py',
   'PYMODULE'),
  ('certifi',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\certifi\\__init__.py',
   'PYMODULE'),
  ('certifi.core',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\certifi\\core.py',
   'PYMODULE'),
  ('requests.packages',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\requests\\packages.py',
   'PYMODULE'),
  ('urllib3.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\urllib3\\exceptions.py',
   'PYMODULE'),
  ('cryptography',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl.binding',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\binding.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\bindings\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\__init__.py',
   'PYMODULE'),
  ('cryptography.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\exceptions.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl._conditional',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\_conditional.py',
   'PYMODULE'),
  ('cryptography.hazmat.bindings.openssl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\bindings\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\backends\\openssl\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\utils.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x25519',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._serialization',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\primitives\\_serialization.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.x448',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\x448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed25519',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed25519.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ed448',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ed448.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dsa',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.dh',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\dh.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.rsa',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._asymmetric',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\primitives\\_asymmetric.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.ec',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat._oid',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\_oid.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.hashes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\primitives\\hashes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\primitives\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.types',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\types.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.ssh',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\ssh.py',
   'PYMODULE'),
  ('bcrypt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\bcrypt\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.modes',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\modes.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives._cipheralgorithm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\primitives\\_cipheralgorithm.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.algorithms',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\algorithms.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\base.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.asymmetric.padding',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\primitives\\asymmetric\\padding.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.constant_time',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\primitives\\constant_time.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.rsa',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\backends\\openssl\\rsa.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ec',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ec.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.decode_asn1',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\backends\\openssl\\decode_asn1.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.cmac',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\backends\\openssl\\cmac.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.ciphers',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\backends\\openssl\\ciphers.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.backend',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\backends\\openssl\\backend.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.serialization.pkcs12',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\primitives\\serialization\\pkcs12.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl.aead',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\backends\\openssl\\aead.py',
   'PYMODULE'),
  ('cryptography.hazmat.primitives.ciphers.aead',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\primitives\\ciphers\\aead.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends.openssl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\backends\\openssl\\__init__.py',
   'PYMODULE'),
  ('cryptography.hazmat.backends',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\backends\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\x509\\__init__.py',
   'PYMODULE'),
  ('cryptography.x509.oid',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\x509\\oid.py',
   'PYMODULE'),
  ('cryptography.x509.name',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\x509\\name.py',
   'PYMODULE'),
  ('cryptography.x509.general_name',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\x509\\general_name.py',
   'PYMODULE'),
  ('cryptography.x509.extensions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\x509\\extensions.py',
   'PYMODULE'),
  ('cryptography.x509.base',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\x509\\base.py',
   'PYMODULE'),
  ('cryptography.x509.certificate_transparency',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\x509\\certificate_transparency.py',
   'PYMODULE'),
  ('cryptography.utils',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\utils.py',
   'PYMODULE'),
  ('cryptography.__about__',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\__about__.py',
   'PYMODULE'),
  ('urllib3.contrib.pyopenssl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\urllib3\\contrib\\pyopenssl.py',
   'PYMODULE'),
  ('OpenSSL.crypto',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\OpenSSL\\crypto.py',
   'PYMODULE'),
  ('OpenSSL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\OpenSSL\\__init__.py',
   'PYMODULE'),
  ('OpenSSL.version',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\OpenSSL\\version.py',
   'PYMODULE'),
  ('OpenSSL._util',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\OpenSSL\\_util.py',
   'PYMODULE'),
  ('OpenSSL.SSL',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\OpenSSL\\SSL.py',
   'PYMODULE'),
  ('urllib3.contrib',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\urllib3\\contrib\\__init__.py',
   'PYMODULE'),
  ('requests.exceptions',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\requests\\exceptions.py',
   'PYMODULE'),
  ('urllib3',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\urllib3\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\urllib3\\contrib\\emscripten\\__init__.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.connection',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\urllib3\\contrib\\emscripten\\connection.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.response',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\urllib3\\contrib\\emscripten\\response.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.request',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\urllib3\\contrib\\emscripten\\request.py',
   'PYMODULE'),
  ('urllib3.contrib.emscripten.fetch',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\urllib3\\contrib\\emscripten\\fetch.py',
   'PYMODULE'),
  ('PySide6',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\__init__.py',
   'PYMODULE'),
  ('PySide6.support.deprecated',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\support\\deprecated.py',
   'PYMODULE'),
  ('PySide6.support',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\support\\__init__.py',
   'PYMODULE'),
  ('shiboken6',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\shiboken6\\__init__.py',
   'PYMODULE'),
  ('main_window',
   'E:\\ai编程开发\\augument-mail-plugin\\main_window.py',
   'PYMODULE'),
  ('auth_manager',
   'E:\\ai编程开发\\augument-mail-plugin\\auth_manager.py',
   'PYMODULE'),
  ('usage_limiter',
   'E:\\ai编程开发\\augument-mail-plugin\\usage_limiter.py',
   'PYMODULE'),
  ('code_extractor',
   'E:\\ai编程开发\\augument-mail-plugin\\code_extractor.py',
   'PYMODULE'),
  ('tempmail_api',
   'E:\\ai编程开发\\augument-mail-plugin\\tempmail_api.py',
   'PYMODULE'),
  ('main_page', 'E:\\ai编程开发\\augument-mail-plugin\\main_page.py', 'PYMODULE'),
  ('auth_page', 'E:\\ai编程开发\\augument-mail-plugin\\auth_page.py', 'PYMODULE'),
  ('config', 'E:\\ai编程开发\\augument-mail-plugin\\config.py', 'PYMODULE'),
  ('logging',
   'D:\\ProgramData\\anaconda3\\Lib\\logging\\__init__.py',
   'PYMODULE')],
 [('api-ms-win-crt-heap-l1-1-0.dll',
   'D:\\ProgramData\\anaconda3\\api-ms-win-crt-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-math-l1-1-0.dll',
   'D:\\ProgramData\\anaconda3\\api-ms-win-crt-math-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-runtime-l1-1-0.dll',
   'D:\\ProgramData\\anaconda3\\api-ms-win-crt-runtime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-locale-l1-1-0.dll',
   'D:\\ProgramData\\anaconda3\\api-ms-win-crt-locale-l1-1-0.dll',
   'BINARY'),
  ('python311.dll', 'D:\\ProgramData\\anaconda3\\python311.dll', 'BINARY'),
  ('VCRUNTIME140.dll',
   'D:\\ProgramData\\anaconda3\\VCRUNTIME140.dll',
   'BINARY'),
  ('api-ms-win-crt-stdio-l1-1-0.dll',
   'D:\\ProgramData\\anaconda3\\api-ms-win-crt-stdio-l1-1-0.dll',
   'BINARY'),
  ('ucrtbase.dll', 'D:\\ProgramData\\anaconda3\\ucrtbase.dll', 'BINARY'),
  ('api-ms-win-crt-conio-l1-1-0.dll',
   'D:\\ProgramData\\anaconda3\\api-ms-win-crt-conio-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-convert-l1-1-0.dll',
   'D:\\ProgramData\\anaconda3\\api-ms-win-crt-convert-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-environment-l1-1-0.dll',
   'D:\\ProgramData\\anaconda3\\api-ms-win-crt-environment-l1-1-0.dll',
   'BINARY'),
  ('zlib.dll', 'D:\\ProgramData\\anaconda3\\zlib.dll', 'BINARY'),
  ('api-ms-win-crt-time-l1-1-0.dll',
   'D:\\ProgramData\\anaconda3\\api-ms-win-crt-time-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-process-l1-1-0.dll',
   'D:\\ProgramData\\anaconda3\\api-ms-win-crt-process-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-string-l1-1-0.dll',
   'D:\\ProgramData\\anaconda3\\api-ms-win-crt-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-crt-filesystem-l1-1-0.dll',
   'D:\\ProgramData\\anaconda3\\api-ms-win-crt-filesystem-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-namedpipe-l1-1-0.dll',
   'D:\\ProgramData\\anaconda3\\api-ms-win-core-namedpipe-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-1.dll',
   'D:\\ProgramData\\anaconda3\\api-ms-win-core-processthreads-l1-1-1.dll',
   'BINARY'),
  ('api-ms-win-core-util-l1-1-0.dll',
   'D:\\ProgramData\\anaconda3\\api-ms-win-core-util-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processenvironment-l1-1-0.dll',
   'D:\\ProgramData\\anaconda3\\api-ms-win-core-processenvironment-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-2-0.dll',
   'D:\\ProgramData\\anaconda3\\api-ms-win-core-synch-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-handle-l1-1-0.dll',
   'D:\\ProgramData\\anaconda3\\api-ms-win-core-handle-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-heap-l1-1-0.dll',
   'D:\\ProgramData\\anaconda3\\api-ms-win-core-heap-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-libraryloader-l1-1-0.dll',
   'D:\\ProgramData\\anaconda3\\api-ms-win-core-libraryloader-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-processthreads-l1-1-0.dll',
   'D:\\ProgramData\\anaconda3\\api-ms-win-core-processthreads-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-sysinfo-l1-1-0.dll',
   'D:\\ProgramData\\anaconda3\\api-ms-win-core-sysinfo-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-string-l1-1-0.dll',
   'D:\\ProgramData\\anaconda3\\api-ms-win-core-string-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-debug-l1-1-0.dll',
   'D:\\ProgramData\\anaconda3\\api-ms-win-core-debug-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-2-0.dll',
   'D:\\ProgramData\\anaconda3\\api-ms-win-core-file-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-profile-l1-1-0.dll',
   'D:\\ProgramData\\anaconda3\\api-ms-win-core-profile-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-errorhandling-l1-1-0.dll',
   'D:\\ProgramData\\anaconda3\\api-ms-win-core-errorhandling-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-localization-l1-2-0.dll',
   'D:\\ProgramData\\anaconda3\\api-ms-win-core-localization-l1-2-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l2-1-0.dll',
   'D:\\ProgramData\\anaconda3\\api-ms-win-core-file-l2-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-datetime-l1-1-0.dll',
   'D:\\ProgramData\\anaconda3\\api-ms-win-core-datetime-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-memory-l1-1-0.dll',
   'D:\\ProgramData\\anaconda3\\api-ms-win-core-memory-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-console-l1-1-0.dll',
   'D:\\ProgramData\\anaconda3\\api-ms-win-core-console-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-interlocked-l1-1-0.dll',
   'D:\\ProgramData\\anaconda3\\api-ms-win-core-interlocked-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-file-l1-1-0.dll',
   'D:\\ProgramData\\anaconda3\\api-ms-win-core-file-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-timezone-l1-1-0.dll',
   'D:\\ProgramData\\anaconda3\\api-ms-win-core-timezone-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-synch-l1-1-0.dll',
   'D:\\ProgramData\\anaconda3\\api-ms-win-core-synch-l1-1-0.dll',
   'BINARY'),
  ('api-ms-win-core-rtlsupport-l1-1-0.dll',
   'D:\\ProgramData\\anaconda3\\api-ms-win-core-rtlsupport-l1-1-0.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qjpeg.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\plugins\\imageformats\\qjpeg.dll',
   'BINARY'),
  ('PySide6\\plugins\\iconengines\\qsvgicon.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\plugins\\iconengines\\qsvgicon.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qico.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\plugins\\imageformats\\qico.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qsvg.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\plugins\\imageformats\\qsvg.dll',
   'BINARY'),
  ('PySide6\\plugins\\platforms\\qminimal.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\plugins\\platforms\\qminimal.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qpdf.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\plugins\\imageformats\\qpdf.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qgif.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\plugins\\imageformats\\qgif.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qtiff.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\plugins\\imageformats\\qtiff.dll',
   'BINARY'),
  ('PySide6\\plugins\\platforms\\qwindows.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\plugins\\platforms\\qwindows.dll',
   'BINARY'),
  ('PySide6\\opengl32sw.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\opengl32sw.dll',
   'BINARY'),
  ('PySide6\\plugins\\platforms\\qdirect2d.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\plugins\\platforms\\qdirect2d.dll',
   'BINARY'),
  ('PySide6\\plugins\\tls\\qopensslbackend.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\plugins\\tls\\qopensslbackend.dll',
   'BINARY'),
  ('PySide6\\plugins\\networkinformation\\qnetworklistmanager.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\plugins\\networkinformation\\qnetworklistmanager.dll',
   'BINARY'),
  ('PySide6\\plugins\\tls\\qcertonlybackend.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\plugins\\tls\\qcertonlybackend.dll',
   'BINARY'),
  ('PySide6\\plugins\\styles\\qwindowsvistastyle.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\plugins\\styles\\qwindowsvistastyle.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qtga.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\plugins\\imageformats\\qtga.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qwebp.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\plugins\\imageformats\\qwebp.dll',
   'BINARY'),
  ('PySide6\\plugins\\tls\\qschannelbackend.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\plugins\\tls\\qschannelbackend.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qicns.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\plugins\\imageformats\\qicns.dll',
   'BINARY'),
  ('PySide6\\plugins\\generic\\qtuiotouchplugin.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\plugins\\generic\\qtuiotouchplugin.dll',
   'BINARY'),
  ('PySide6\\plugins\\imageformats\\qwbmp.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\plugins\\imageformats\\qwbmp.dll',
   'BINARY'),
  ('PySide6\\plugins\\platforms\\qoffscreen.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\plugins\\platforms\\qoffscreen.dll',
   'BINARY'),
  ('PySide6\\plugins\\platforminputcontexts\\qtvirtualkeyboardplugin.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\plugins\\platforminputcontexts\\qtvirtualkeyboardplugin.dll',
   'BINARY'),
  ('select.pyd', 'D:\\ProgramData\\anaconda3\\DLLs\\select.pyd', 'EXTENSION'),
  ('_hashlib.pyd',
   'D:\\ProgramData\\anaconda3\\DLLs\\_hashlib.pyd',
   'EXTENSION'),
  ('_lzma.pyd', 'D:\\ProgramData\\anaconda3\\DLLs\\_lzma.pyd', 'EXTENSION'),
  ('_bz2.pyd', 'D:\\ProgramData\\anaconda3\\DLLs\\_bz2.pyd', 'EXTENSION'),
  ('pyexpat.pyd', 'D:\\ProgramData\\anaconda3\\DLLs\\pyexpat.pyd', 'EXTENSION'),
  ('_ssl.pyd', 'D:\\ProgramData\\anaconda3\\DLLs\\_ssl.pyd', 'EXTENSION'),
  ('_decimal.pyd',
   'D:\\ProgramData\\anaconda3\\DLLs\\_decimal.pyd',
   'EXTENSION'),
  ('_multiprocessing.pyd',
   'D:\\ProgramData\\anaconda3\\DLLs\\_multiprocessing.pyd',
   'EXTENSION'),
  ('_socket.pyd', 'D:\\ProgramData\\anaconda3\\DLLs\\_socket.pyd', 'EXTENSION'),
  ('_ctypes.pyd', 'D:\\ProgramData\\anaconda3\\DLLs\\_ctypes.pyd', 'EXTENSION'),
  ('_queue.pyd', 'D:\\ProgramData\\anaconda3\\DLLs\\_queue.pyd', 'EXTENSION'),
  ('unicodedata.pyd',
   'D:\\ProgramData\\anaconda3\\DLLs\\unicodedata.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md__mypyc.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\charset_normalizer\\md__mypyc.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('charset_normalizer\\md.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\charset_normalizer\\md.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_overlapped.pyd',
   'D:\\ProgramData\\anaconda3\\DLLs\\_overlapped.pyd',
   'EXTENSION'),
  ('_asyncio.pyd',
   'D:\\ProgramData\\anaconda3\\DLLs\\_asyncio.pyd',
   'EXTENSION'),
  ('lxml\\etree.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\lxml\\etree.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\_elementpath.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\lxml\\_elementpath.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\sax.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\lxml\\sax.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\objectify.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\lxml\\objectify.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('lxml\\html\\diff.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\lxml\\html\\diff.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('win32\\win32evtlog.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\win32\\win32evtlog.pyd',
   'EXTENSION'),
  ('win32\\win32api.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\win32\\win32api.pyd',
   'EXTENSION'),
  ('lxml\\builder.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\lxml\\builder.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('zstandard\\_cffi.cp311-win_amd64.pyd',
   'D:\\ProgramData\\anaconda3\\Lib\\site-packages\\zstandard\\_cffi.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('zstandard\\backend_c.cp311-win_amd64.pyd',
   'D:\\ProgramData\\anaconda3\\Lib\\site-packages\\zstandard\\backend_c.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_brotli.cp311-win_amd64.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\_brotli.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('_cffi_backend.cp311-win_amd64.pyd',
   'D:\\ProgramData\\anaconda3\\Lib\\site-packages\\_cffi_backend.cp311-win_amd64.pyd',
   'EXTENSION'),
  ('cryptography\\hazmat\\bindings\\_rust.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\cryptography\\hazmat\\bindings\\_rust.pyd',
   'EXTENSION'),
  ('bcrypt\\_bcrypt.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\bcrypt\\_bcrypt.pyd',
   'EXTENSION'),
  ('PySide6\\QtNetwork.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\QtNetwork.pyd',
   'EXTENSION'),
  ('shiboken6\\Shiboken.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\shiboken6\\Shiboken.pyd',
   'EXTENSION'),
  ('PySide6\\QtGui.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\QtGui.pyd',
   'EXTENSION'),
  ('PySide6\\QtCore.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\QtCore.pyd',
   'EXTENSION'),
  ('PySide6\\QtWidgets.pyd',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\QtWidgets.pyd',
   'EXTENSION'),
  ('PySide6\\Qt6Gui.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\Qt6Gui.dll',
   'BINARY'),
  ('PySide6\\Qt6Core.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\Qt6Core.dll',
   'BINARY'),
  ('PySide6\\Qt6Svg.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\Qt6Svg.dll',
   'BINARY'),
  ('PySide6\\Qt6Pdf.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\Qt6Pdf.dll',
   'BINARY'),
  ('api-ms-win-crt-utility-l1-1-0.dll',
   'D:\\ProgramData\\anaconda3\\api-ms-win-crt-utility-l1-1-0.dll',
   'BINARY'),
  ('MSVCP140.dll', 'D:\\ProgramData\\anaconda3\\MSVCP140.dll', 'BINARY'),
  ('VCRUNTIME140_1.dll',
   'D:\\ProgramData\\anaconda3\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PySide6\\Qt6Network.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\Qt6Network.dll',
   'BINARY'),
  ('PySide6\\Qt6Widgets.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\Qt6Widgets.dll',
   'BINARY'),
  ('PySide6\\Qt6VirtualKeyboard.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\Qt6VirtualKeyboard.dll',
   'BINARY'),
  ('libcrypto-3-x64.dll',
   'D:\\ProgramData\\anaconda3\\Library\\bin\\libcrypto-3-x64.dll',
   'BINARY'),
  ('liblzma.dll',
   'D:\\ProgramData\\anaconda3\\Library\\bin\\liblzma.dll',
   'BINARY'),
  ('LIBBZ2.dll',
   'D:\\ProgramData\\anaconda3\\Library\\bin\\LIBBZ2.dll',
   'BINARY'),
  ('libssl-3-x64.dll',
   'D:\\ProgramData\\anaconda3\\Library\\bin\\libssl-3-x64.dll',
   'BINARY'),
  ('ffi.dll', 'D:\\ProgramData\\anaconda3\\Library\\bin\\ffi.dll', 'BINARY'),
  ('pywin32_system32\\pywintypes311.dll',
   'D:\\ProgramData\\anaconda3\\Library\\bin\\pywintypes311.dll',
   'BINARY'),
  ('python3.dll', 'D:\\ProgramData\\anaconda3\\python3.dll', 'BINARY'),
  ('PySide6\\pyside6.abi3.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\pyside6.abi3.dll',
   'BINARY'),
  ('PySide6\\VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PySide6\\MSVCP140.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\MSVCP140.dll',
   'BINARY'),
  ('shiboken6\\shiboken6.abi3.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\shiboken6\\shiboken6.abi3.dll',
   'BINARY'),
  ('shiboken6\\VCRUNTIME140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\shiboken6\\VCRUNTIME140_1.dll',
   'BINARY'),
  ('PySide6\\MSVCP140_2.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\MSVCP140_2.dll',
   'BINARY'),
  ('PySide6\\MSVCP140_1.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\MSVCP140_1.dll',
   'BINARY'),
  ('PySide6\\Qt6Qml.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\Qt6Qml.dll',
   'BINARY'),
  ('PySide6\\Qt6Quick.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\Qt6Quick.dll',
   'BINARY'),
  ('PySide6\\Qt6OpenGL.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\Qt6OpenGL.dll',
   'BINARY'),
  ('PySide6\\Qt6QmlModels.dll',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\Qt6QmlModels.dll',
   'BINARY')],
 [],
 [],
 [('base_library.zip',
   'E:\\ai编程开发\\augument-mail-plugin\\build\\TempMailTool\\base_library.zip',
   'DATA'),
  ('PySide6\\translations\\qt_en.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qt_en.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_en.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qtbase_en.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_it.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qtbase_it.qm',
   'DATA'),
  ('cryptography-41.0.7.dist-info\\top_level.txt',
   'c:\\users\\<USER>\\appdata\\roaming\\python\\python311\\site-packages\\cryptography-41.0.7.dist-info\\top_level.txt',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_skeleton_for_xslt1.xsl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_skeleton_for_xslt1.xsl',
   'DATA'),
  ('PySide6\\translations\\qt_nl.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qt_nl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_cs.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qt_help_cs.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_fi.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qtbase_fi.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_zh_TW.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qt_help_zh_TW.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_hu.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qt_help_hu.qm',
   'DATA'),
  ('PySide6\\translations\\qt_fi.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qt_fi.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_hr.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qt_help_hr.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_uk.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qtbase_uk.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_es.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qt_help_es.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_pt_BR.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qtbase_pt_BR.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ko.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qtbase_ko.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_de.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qt_help_de.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_es.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qtbase_es.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_zh_CN.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qt_help_zh_CN.qm',
   'DATA'),
  ('PySide6\\translations\\qt_es.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qt_es.qm',
   'DATA'),
  ('PySide6\\translations\\qt_lv.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qt_lv.qm',
   'DATA'),
  ('cryptography-41.0.7.dist-info\\WHEEL',
   'c:\\users\\<USER>\\appdata\\roaming\\python\\python311\\site-packages\\cryptography-41.0.7.dist-info\\WHEEL',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_abstract_expand.xsl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_abstract_expand.xsl',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_dsdl_include.xsl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_dsdl_include.xsl',
   'DATA'),
  ('PySide6\\translations\\qt_ko.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qt_ko.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ar.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qt_ar.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ca.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qt_help_ca.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_fr.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qtbase_fr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ru.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qt_ru.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_da.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qt_help_da.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_hr.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qtbase_hr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_pl.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qt_pl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_tr.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qt_tr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_sl.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qt_help_sl.qm',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\RNG2Schtrn.xsl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\lxml\\isoschematron\\resources\\xsl\\RNG2Schtrn.xsl',
   'DATA'),
  ('PySide6\\translations\\qtbase_zh_TW.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qtbase_zh_TW.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_he.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qtbase_he.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ru.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qt_help_ru.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_pt_BR.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qt_help_pt_BR.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ru.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qtbase_ru.qm',
   'DATA'),
  ('PySide6\\translations\\qt_bg.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qt_bg.qm',
   'DATA'),
  ('cryptography-41.0.7.dist-info\\LICENSE.APACHE',
   'c:\\users\\<USER>\\appdata\\roaming\\python\\python311\\site-packages\\cryptography-41.0.7.dist-info\\LICENSE.APACHE',
   'DATA'),
  ('PySide6\\translations\\qtbase_sk.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qtbase_sk.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_bg.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qt_help_bg.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_nn.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qtbase_nn.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_pl.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qtbase_pl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_tr.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qt_help_tr.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_de.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qtbase_de.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ca.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qtbase_ca.qm',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\readme.txt',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\readme.txt',
   'DATA'),
  ('PySide6\\translations\\qt_help_gl.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qt_help_gl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_pt_PT.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qt_pt_PT.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_en.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qt_help_en.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ko.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qt_help_ko.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_bg.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qtbase_bg.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_nn.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qt_help_nn.qm',
   'DATA'),
  ('cryptography-41.0.7.dist-info\\REQUESTED',
   'c:\\users\\<USER>\\appdata\\roaming\\python\\python311\\site-packages\\cryptography-41.0.7.dist-info\\REQUESTED',
   'DATA'),
  ('PySide6\\translations\\qt_fr.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qt_fr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_pt_BR.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qt_pt_BR.qm',
   'DATA'),
  ('PySide6\\translations\\qt_sv.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qt_sv.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ca.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qt_ca.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_ja.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qt_help_ja.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_da.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qtbase_da.qm',
   'DATA'),
  ('PySide6\\translations\\qt_lt.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qt_lt.qm',
   'DATA'),
  ('cryptography-41.0.7.dist-info\\RECORD',
   'c:\\users\\<USER>\\appdata\\roaming\\python\\python311\\site-packages\\cryptography-41.0.7.dist-info\\RECORD',
   'DATA'),
  ('PySide6\\translations\\qt_help_it.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qt_help_it.qm',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\XSD2Schtrn.xsl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\lxml\\isoschematron\\resources\\xsl\\XSD2Schtrn.xsl',
   'DATA'),
  ('PySide6\\translations\\qtbase_lv.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qtbase_lv.qm',
   'DATA'),
  ('PySide6\\translations\\qt_zh_TW.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qt_zh_TW.qm',
   'DATA'),
  ('cryptography-41.0.7.dist-info\\LICENSE.BSD',
   'c:\\users\\<USER>\\appdata\\roaming\\python\\python311\\site-packages\\cryptography-41.0.7.dist-info\\LICENSE.BSD',
   'DATA'),
  ('PySide6\\translations\\qt_zh_CN.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qt_zh_CN.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ja.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qtbase_ja.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_tr.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qtbase_tr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_de.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qt_de.qm',
   'DATA'),
  ('certifi\\cacert.pem',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\certifi\\cacert.pem',
   'DATA'),
  ('lxml\\isoschematron\\resources\\rng\\iso-schematron.rng',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\lxml\\isoschematron\\resources\\rng\\iso-schematron.rng',
   'DATA'),
  ('PySide6\\translations\\qt_help_ar.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qt_help_ar.qm',
   'DATA'),
  ('PySide6\\translations\\qt_sk.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qt_sk.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_hu.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qtbase_hu.qm',
   'DATA'),
  ('PySide6\\translations\\qt_it.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qt_it.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_fa.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qtbase_fa.qm',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_message.xsl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_schematron_message.xsl',
   'DATA'),
  ('PySide6\\translations\\qt_nn.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qt_nn.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_cs.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qtbase_cs.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_sk.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qt_help_sk.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_ar.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qtbase_ar.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_gd.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qtbase_gd.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_nl.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qtbase_nl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_ja.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qt_ja.qm',
   'DATA'),
  ('PySide6\\translations\\qt_da.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qt_da.qm',
   'DATA'),
  ('PySide6\\translations\\qt_hu.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qt_hu.qm',
   'DATA'),
  ('PySide6\\translations\\qt_sl.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qt_sl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_fr.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qt_help_fr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_hr.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qt_hr.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_pl.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qt_help_pl.qm',
   'DATA'),
  ('PySide6\\translations\\qt_gl.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qt_gl.qm',
   'DATA'),
  ('cryptography-41.0.7.dist-info\\LICENSE',
   'c:\\users\\<USER>\\appdata\\roaming\\python\\python311\\site-packages\\cryptography-41.0.7.dist-info\\LICENSE',
   'DATA'),
  ('cryptography-41.0.7.dist-info\\METADATA',
   'c:\\users\\<USER>\\appdata\\roaming\\python\\python311\\site-packages\\cryptography-41.0.7.dist-info\\METADATA',
   'DATA'),
  ('PySide6\\translations\\qt_help_nl.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qt_help_nl.qm',
   'DATA'),
  ('certifi\\py.typed',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\certifi\\py.typed',
   'DATA'),
  ('PySide6\\translations\\qt_fa.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qt_fa.qm',
   'DATA'),
  ('PySide6\\translations\\qtbase_zh_CN.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qtbase_zh_CN.qm',
   'DATA'),
  ('PySide6\\translations\\qt_gd.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qt_gd.qm',
   'DATA'),
  ('lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_svrl_for_xslt1.xsl',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\lxml\\isoschematron\\resources\\xsl\\iso-schematron-xslt1\\iso_svrl_for_xslt1.xsl',
   'DATA'),
  ('PySide6\\translations\\qt_cs.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qt_cs.qm',
   'DATA'),
  ('PySide6\\translations\\qt_he.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qt_he.qm',
   'DATA'),
  ('PySide6\\translations\\qt_uk.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qt_uk.qm',
   'DATA'),
  ('PySide6\\translations\\qt_help_uk.qm',
   'C:\\Users\\<USER>\\AppData\\Roaming\\Python\\Python311\\site-packages\\PySide6\\translations\\qt_help_uk.qm',
   'DATA'),
  ('cryptography-41.0.7.dist-info\\INSTALLER',
   'c:\\users\\<USER>\\appdata\\roaming\\python\\python311\\site-packages\\cryptography-41.0.7.dist-info\\INSTALLER',
   'DATA'),
  ('h2-4.2.0.dist-info\\RECORD',
   'c:\\users\\<USER>\\appdata\\roaming\\python\\python311\\site-packages\\h2-4.2.0.dist-info\\RECORD',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\WHEEL',
   'c:\\users\\<USER>\\appdata\\roaming\\python\\python311\\site-packages\\pyreadline3-3.5.4.dist-info\\WHEEL',
   'DATA'),
  ('h2-4.2.0.dist-info\\INSTALLER',
   'c:\\users\\<USER>\\appdata\\roaming\\python\\python311\\site-packages\\h2-4.2.0.dist-info\\INSTALLER',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\RECORD',
   'c:\\users\\<USER>\\appdata\\roaming\\python\\python311\\site-packages\\pyreadline3-3.5.4.dist-info\\RECORD',
   'DATA'),
  ('h2-4.2.0.dist-info\\LICENSE',
   'c:\\users\\<USER>\\appdata\\roaming\\python\\python311\\site-packages\\h2-4.2.0.dist-info\\LICENSE',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\LICENSE.md',
   'c:\\users\\<USER>\\appdata\\roaming\\python\\python311\\site-packages\\pyreadline3-3.5.4.dist-info\\LICENSE.md',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\top_level.txt',
   'c:\\users\\<USER>\\appdata\\roaming\\python\\python311\\site-packages\\pyreadline3-3.5.4.dist-info\\top_level.txt',
   'DATA'),
  ('h2-4.2.0.dist-info\\WHEEL',
   'c:\\users\\<USER>\\appdata\\roaming\\python\\python311\\site-packages\\h2-4.2.0.dist-info\\WHEEL',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\INSTALLER',
   'c:\\users\\<USER>\\appdata\\roaming\\python\\python311\\site-packages\\pyreadline3-3.5.4.dist-info\\INSTALLER',
   'DATA'),
  ('pyreadline3-3.5.4.dist-info\\METADATA',
   'c:\\users\\<USER>\\appdata\\roaming\\python\\python311\\site-packages\\pyreadline3-3.5.4.dist-info\\METADATA',
   'DATA'),
  ('h2-4.2.0.dist-info\\METADATA',
   'c:\\users\\<USER>\\appdata\\roaming\\python\\python311\\site-packages\\h2-4.2.0.dist-info\\METADATA',
   'DATA'),
  ('h2-4.2.0.dist-info\\top_level.txt',
   'c:\\users\\<USER>\\appdata\\roaming\\python\\python311\\site-packages\\h2-4.2.0.dist-info\\top_level.txt',
   'DATA')],
 [])
