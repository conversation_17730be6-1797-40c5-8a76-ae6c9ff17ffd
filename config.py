"""
配置文件
"""

# TempMail API 配置
TEMPMAIL_BASE_URL = "https://tempmail.plus"
TEMPMAIL_API_URL = f"{TEMPMAIL_BASE_URL}/api/mails"
DEFAULT_EPIN = "123456"  # 默认邮箱PIN码
REAL_EMAIL = "<EMAIL>"  # 实际用于获取邮件的邮箱地址
DEFAULT_EMAIL = "<EMAIL>"  # 默认邮箱地址

# 请求配置
REQUEST_TIMEOUT = 10  # 请求超时时间（秒）
MAX_RETRIES = 3      # 最大重试次数

# 轮询配置
POLL_INTERVAL = 3    # 轮询间隔（秒）
MAX_POLL_TIME = 300  # 最大轮询时间（秒）

# 使用限制配置
DAILY_USAGE_LIMIT = 5  # 每日使用次数限制
USAGE_DATA_FILE = "usage_data.json"  # 使用数据存储文件

# GUI 配置
WINDOW_TITLE = "Augment Code - 激活额度获取工具"
WINDOW_WIDTH = 520
WINDOW_HEIGHT = 480
WINDOW_MIN_WIDTH = 480
WINDOW_MIN_HEIGHT = 480

# 授权配置
AUTH_CODE = "111111"  # 授权码

# 邮件显示配置
EMAIL_LIMIT = 20     # 每次获取的邮件数量限制

# 验证码提取配置
CODE_PATTERNS = [
    r'\b\d{4,8}\b',                    # 4-8位数字
    r'\b[A-Z0-9]{4,8}\b',              # 4-8位大写字母和数字
    r'验证码[：:]\s*([A-Z0-9]{4,8})',    # 中文验证码格式
    r'verification code[：:]\s*([A-Z0-9]{4,8})',  # 英文验证码格式
    r'code[：:]\s*([A-Z0-9]{4,8})',     # 简单code格式
]

# 日志配置
LOG_LEVEL = "INFO"
LOG_FORMAT = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
