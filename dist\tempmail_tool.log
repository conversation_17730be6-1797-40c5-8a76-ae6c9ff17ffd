2025-08-01 11:44:07,345 - __main__ - INFO - ==================================================
2025-08-01 11:44:07,345 - __main__ - INFO - TempMail 验证码获取工具启动
2025-08-01 11:44:07,345 - __main__ - INFO - ==================================================
2025-08-01 11:44:07,372 - __main__ - INFO - 创建主窗口...
2025-08-01 11:44:08,137 - auth_manager - INFO - 未找到授权数据
2025-08-01 11:44:08,137 - main_window - INFO - 未检测到有效授权，显示授权页面
2025-08-01 11:44:08,416 - __main__ - INFO - 主窗口已显示
2025-08-01 11:44:08,417 - __main__ - INFO - 应用程序配置:
2025-08-01 11:44:08,417 - __main__ - INFO -   - 窗口大小: 520x480
2025-08-01 11:44:08,417 - __main__ - INFO -   - 每日使用限制: 5
2025-08-01 11:44:08,417 - __main__ - INFO -   - 请求超时: 10秒
2025-08-01 11:44:08,417 - __main__ - INFO -   - 日志级别: INFO
2025-08-01 11:44:08,417 - __main__ - INFO - 应用程序开始运行...
2025-08-01 11:44:13,888 - auth_manager - INFO - 授权状态已保存
2025-08-01 11:44:13,888 - auth_manager - INFO - 授权状态保存成功
2025-08-01 11:44:13,888 - main_window - INFO - 授权状态已保存
2025-08-01 11:44:44,694 - usage_limiter - INFO - 记录使用: <EMAIL> - get_quota, 今日已使用: 1/5
2025-08-01 11:44:44,696 - tempmail_api - INFO - 正在获取邮箱 <EMAIL> 的邮件...
2025-08-01 11:44:52,490 - tempmail_api - INFO - 成功获取 20 封邮件
2025-08-01 11:44:52,491 - tempmail_api - INFO - 正在获取邮件 3702594142 的详细内容...
2025-08-01 11:44:54,116 - tempmail_api - INFO - 成功获取邮件详情，主题: Welcome to Augment Code
2025-08-01 11:44:59,130 - __main__ - INFO - 应用程序退出，退出码: 0
