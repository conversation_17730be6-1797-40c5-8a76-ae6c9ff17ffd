"""
使用次数控制模块
实现每日使用次数限制功能，防止滥用
"""

import json
import os
import logging
from datetime import datetime, date, timedelta
from typing import Dict, Optional
import config
from app_paths import get_usage_file_path

# 设置日志
logging.basicConfig(level=getattr(logging, config.LOG_LEVEL), format=config.LOG_FORMAT)
logger = logging.getLogger(__name__)


class UsageLimiter:
    """使用次数限制器"""
    
    def __init__(self, data_file: str = None):
        self.data_file = data_file or get_usage_file_path()
        self.daily_limit = config.DAILY_USAGE_LIMIT
        self.usage_data = self._load_usage_data()
    
    def _load_usage_data(self) -> Dict:
        """加载使用数据"""
        if not os.path.exists(self.data_file):
            return {}
        
        try:
            with open(self.data_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                logger.info(f"已加载使用数据: {len(data)} 条记录")
                return data
        except Exception as e:
            logger.error(f"加载使用数据失败: {e}")
            return {}
    
    def _save_usage_data(self):
        """保存使用数据"""
        try:
            # 确保目录存在
            os.makedirs(os.path.dirname(self.data_file) if os.path.dirname(self.data_file) else '.', exist_ok=True)
            
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(self.usage_data, f, ensure_ascii=False, indent=2)
                logger.debug("使用数据已保存")
        except Exception as e:
            logger.error(f"保存使用数据失败: {e}")
    
    def _get_today_key(self) -> str:
        """获取今天的日期键"""
        return date.today().isoformat()
    
    def _cleanup_old_data(self):
        """清理旧数据（保留最近30天）"""
        today = date.today()
        cutoff_days = 30
        
        keys_to_remove = []
        for date_key in self.usage_data.keys():
            try:
                record_date = datetime.fromisoformat(date_key).date()
                if (today - record_date).days > cutoff_days:
                    keys_to_remove.append(date_key)
            except ValueError:
                # 无效的日期格式，也删除
                keys_to_remove.append(date_key)
        
        for key in keys_to_remove:
            del self.usage_data[key]
        
        if keys_to_remove:
            logger.info(f"清理了 {len(keys_to_remove)} 条旧数据")
            self._save_usage_data()
    
    def get_today_usage(self) -> int:
        """获取今天的使用次数"""
        today_key = self._get_today_key()
        today_data = self.usage_data.get(today_key, {})
        return today_data.get('count', 0)
    
    def get_remaining_usage(self) -> int:
        """获取今天剩余的使用次数"""
        used = self.get_today_usage()
        remaining = max(0, self.daily_limit - used)
        return remaining
    
    def can_use(self) -> bool:
        """检查是否还能使用"""
        return self.get_remaining_usage() > 0
    
    def record_usage(self, email: str = "", operation: str = "get_emails") -> bool:
        """
        记录一次使用
        
        Args:
            email: 使用的邮箱地址
            operation: 操作类型
            
        Returns:
            是否记录成功（如果超出限制则返回False）
        """
        if not self.can_use():
            logger.warning("已达到每日使用限制")
            return False
        
        today_key = self._get_today_key()
        
        # 初始化今天的数据
        if today_key not in self.usage_data:
            self.usage_data[today_key] = {
                'count': 0,
                'operations': []
            }
        
        # 记录使用
        self.usage_data[today_key]['count'] += 1
        self.usage_data[today_key]['operations'].append({
            'timestamp': datetime.now().isoformat(),
            'email': email,
            'operation': operation
        })
        
        # 保存数据
        self._save_usage_data()
        
        # 清理旧数据
        if self.usage_data[today_key]['count'] == 1:  # 每天第一次使用时清理
            self._cleanup_old_data()
        
        logger.info(f"记录使用: {email} - {operation}, 今日已使用: {self.usage_data[today_key]['count']}/{self.daily_limit}")
        return True
    
    def get_usage_stats(self) -> Dict:
        """获取使用统计信息"""
        today_usage = self.get_today_usage()
        remaining = self.get_remaining_usage()
        
        # 计算最近7天的使用情况
        recent_usage = {}
        today = date.today()
        
        for i in range(7):
            check_date = today - timedelta(days=i)
            date_key = check_date.isoformat()
            usage = self.usage_data.get(date_key, {}).get('count', 0)
            recent_usage[date_key] = usage
        
        return {
            'today_usage': today_usage,
            'daily_limit': self.daily_limit,
            'remaining': remaining,
            'recent_7_days': recent_usage,
            'total_records': len(self.usage_data)
        }
    
    def reset_today_usage(self):
        """重置今天的使用次数（管理员功能）"""
        today_key = self._get_today_key()
        if today_key in self.usage_data:
            del self.usage_data[today_key]
            self._save_usage_data()
            logger.info("已重置今日使用次数")
    
    def set_daily_limit(self, new_limit: int):
        """设置每日使用限制"""
        if new_limit > 0:
            self.daily_limit = new_limit
            logger.info(f"每日使用限制已设置为: {new_limit}")
        else:
            logger.error("每日使用限制必须大于0")
    
    def get_today_operations(self) -> list:
        """获取今天的操作记录"""
        today_key = self._get_today_key()
        today_data = self.usage_data.get(today_key, {})
        return today_data.get('operations', [])


# 测试函数
def test_usage_limiter():
    """测试使用次数限制功能"""
    # 使用测试文件
    test_limiter = UsageLimiter("test_usage.json")
    
    print("=== 使用次数限制器测试 ===")
    
    # 获取当前状态
    stats = test_limiter.get_usage_stats()
    print(f"今日使用: {stats['today_usage']}/{stats['daily_limit']}")
    print(f"剩余次数: {stats['remaining']}")
    
    # 测试记录使用
    test_emails = ["<EMAIL>", "<EMAIL>"]
    
    for email in test_emails:
        if test_limiter.can_use():
            success = test_limiter.record_usage(email, "test_operation")
            print(f"记录使用 {email}: {'成功' if success else '失败'}")
        else:
            print(f"无法使用 {email}: 已达到每日限制")
    
    # 显示最终状态
    final_stats = test_limiter.get_usage_stats()
    print(f"\n最终状态:")
    print(f"今日使用: {final_stats['today_usage']}/{final_stats['daily_limit']}")
    print(f"剩余次数: {final_stats['remaining']}")
    
    # 显示今日操作记录
    operations = test_limiter.get_today_operations()
    print(f"\n今日操作记录 ({len(operations)} 条):")
    for op in operations[-3:]:  # 显示最近3条
        print(f"  {op['timestamp'][:19]} - {op['email']} - {op['operation']}")
    
    # 清理测试文件
    try:
        os.remove("test_usage.json")
        print("\n测试文件已清理")
    except:
        pass


if __name__ == "__main__":
    test_usage_limiter()
