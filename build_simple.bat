@echo off
echo ========================================
echo TempMail Tool - Simple Build Script
echo ========================================
echo.

echo Cleaning previous build...
if exist dist rmdir /s /q dist
if exist build rmdir /s /q build

echo Starting build process...
pyinstaller --onefile --windowed --name="TempMailTool" --icon=icon.png main.py

if exist dist\TempMailTool.exe (
    echo.
    echo ========================================
    echo Build successful!
    echo ========================================
    echo Application: dist\TempMailTool.exe
    dir dist\TempMailTool.exe
    echo.
    echo The application is ready to use!
    echo You can distribute this single .exe file.
    echo.
    set /p test_choice="Test the application now? (y/n): "
    if /i "%test_choice%"=="y" (
        echo Starting application...
        start "" "dist\TempMailTool.exe"
    )
) else (
    echo.
    echo ========================================
    echo Build failed!
    echo ========================================
    echo Please check the error messages above.
)

echo.
pause
