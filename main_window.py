"""
主GUI界面 - 重构版本
使用PySide6实现的邮件验证码获取工具界面
"""

import sys
import logging
from datetime import datetime
from typing import List, Dict

from PySide6.QtWidgets import (QApplication, QMainWindow, QStackedWidget, 
                               QStatusBar, QMessageBox)
from PySide6.QtCore import Qt, QThread, Signal
from PySide6.QtGui import QFont

import config
from auth_page import AuthPage
from main_page import MainPage
from tempmail_api import TempMailAPI
from code_extractor import CodeExtractor
from usage_limiter import UsageLimiter
from auth_manager import auth_manager

# 设置日志
logger = logging.getLogger(__name__)


class EmailFetchThread(QThread):
    """邮件获取线程"""
    
    # 信号定义
    status_updated = Signal(str)
    progress_updated = Signal(bool)
    mails_fetched = Signal(list)
    error_occurred = Signal(str)
    
    def __init__(self, email: str):
        super().__init__()
        self.email = email
        self.api = TempMailAPI()
        self.extractor = CodeExtractor()
    
    def run(self):
        """执行邮件获取"""
        try:
            self.status_updated.emit("🔄 连接邮件服务器...")
            self.progress_updated.emit(True)
            
            # 获取邮件
            self.status_updated.emit("📡 获取邮件列表...")
            result = self.api.get_emails(self.email)
            mails = result.get('mails', [])
            
            if mails:
                self.status_updated.emit(f"✅ 获取完成")
                self.mails_fetched.emit(mails)
            else:
                self.status_updated.emit("📭 暂无激活额度")
                self.mails_fetched.emit([])
                
        except Exception as e:
            logging.error(f"获取邮件失败: {e}")
            self.error_occurred.emit(str(e))
        finally:
            self.progress_updated.emit(False)


class MainWindow(QMainWindow):
    """主窗口类"""
    
    def __init__(self):
        super().__init__()

        # 初始化组件
        self.usage_limiter = UsageLimiter()
        self.fetch_thread = None
        self.api = TempMailAPI()
        self.extractor = CodeExtractor()
        self.extractor = CodeExtractor()
        
        # 设置窗口
        self.setup_window()
        self.setup_ui()
        self.setup_connections()
        
        # 设置样式
        self.setStyleSheet(self.get_stylesheet())
    
    def setup_window(self):
        """设置窗口属性"""
        self.setWindowTitle(config.WINDOW_TITLE)
        self.setMinimumSize(config.WINDOW_MIN_WIDTH, config.WINDOW_MIN_HEIGHT)
        self.resize(config.WINDOW_WIDTH, config.WINDOW_HEIGHT)
    
    def setup_ui(self):
        """设置用户界面"""
        # 创建堆叠控件作为中央控件
        self.stacked_widget = QStackedWidget()
        self.setCentralWidget(self.stacked_widget)
        
        # 创建授权页面
        self.auth_page = AuthPage()
        self.stacked_widget.addWidget(self.auth_page)
        
        # 创建主功能页面
        self.main_page = MainPage()
        self.stacked_widget.addWidget(self.main_page)
        
        # 创建状态栏
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)

        # 检查授权状态并设置初始页面
        self.check_initial_auth_status()
    
    def check_initial_auth_status(self):
        """检查初始授权状态"""
        if auth_manager.is_authorized():
            # 已授权，直接进入主页面
            self.stacked_widget.setCurrentIndex(1)
            self.status_bar.showMessage("🟢 已授权")
            self.update_usage_display()
            logger.info("检测到有效授权，直接进入主页面")
        else:
            # 未授权，显示授权页面
            self.stacked_widget.setCurrentIndex(0)
            self.status_bar.showMessage("🔐 请输入授权码")
            logger.info("未检测到有效授权，显示授权页面")

    def setup_connections(self):
        """设置信号连接"""
        # 授权页面信号
        self.auth_page.auth_success.connect(self.on_auth_success)

        # 主页面信号
        self.main_page.fetch_requested.connect(self.start_fetch_process)
    
    def on_auth_success(self):
        """授权成功处理"""
        # 保存授权状态
        if auth_manager.save_auth_success(config.AUTH_CODE):
            logger.info("授权状态已保存")
        else:
            logger.warning("授权状态保存失败")

        # 切换到主页面
        self.stacked_widget.setCurrentIndex(1)
        self.status_bar.showMessage("🟢 已授权")

        # 更新使用次数显示
        self.update_usage_display()
    
    def update_usage_display(self):
        """更新使用次数显示"""
        stats = self.usage_limiter.get_usage_stats()
        self.main_page.update_usage_display(stats)
    
    def start_fetch_process(self, display_email: str):
        """开始获取邮件流程"""
        # 检查使用次数
        if not self.usage_limiter.can_use():
            QMessageBox.warning(self, "使用限制", "今日使用次数已达上限，请明天再试")
            return
        
        # 记录使用
        if not self.usage_limiter.record_usage(display_email, "get_quota"):
            QMessageBox.warning(self, "使用限制", "记录使用次数失败")
            return
        
        # 更新使用次数显示
        self.update_usage_display()
        
        # 使用真实邮箱地址获取邮件
        real_email = config.REAL_EMAIL
        self.email = real_email  # 保存邮箱地址供后续使用

        # 禁用获取按钮
        self.main_page.set_fetch_enabled(False)

        # 启动获取线程
        self.fetch_thread = EmailFetchThread(real_email)
        self.fetch_thread.status_updated.connect(self.main_page.update_status)
        self.fetch_thread.progress_updated.connect(self.main_page.show_progress)
        self.fetch_thread.mails_fetched.connect(self.on_mails_fetched)
        self.fetch_thread.error_occurred.connect(self.on_fetch_error)
        self.fetch_thread.finished.connect(lambda: self.main_page.set_fetch_enabled(True))
        
        self.fetch_thread.start()
    
    def on_mails_fetched(self, mails: List[Dict]):
        """处理获取到的邮件"""
        if not mails:
            self.main_page.update_status("📭 暂无激活额度")
            self.main_page.update_emails_display("📭 该邮箱暂无激活额度\n\n💡 提示：请确保已发送激活额度到此邮箱")
            return
        
        self.main_page.update_status(f"✅ 成功获取 {len(mails)} 个激活额度")
        
        # 提取验证码
        all_codes = []
        display_content = []

        # 只处理最新的5封邮件，避免性能问题
        max_mails_to_process = min(5, len(mails))
        mails_to_process = mails[:max_mails_to_process]

        self.main_page.update_status(f"📧 处理最新 {max_mails_to_process} 封邮件...")

        for i, mail in enumerate(mails_to_process, 1):
            subject = mail.get('subject', '无主题')
            mail_id = mail.get('mail_id')

            # 更新状态
            self.main_page.update_status(f"📧 获取邮件 {i}/{max_mails_to_process} 详情...")

            # 获取邮件详细内容
            if mail_id:
                mail_detail = self.api.get_email_content(str(mail_id), self.email)
                if 'error' not in mail_detail:
                    # 从详细内容中提取验证码
                    html_content = mail_detail.get('html', '')
                    text_content = mail_detail.get('text', '')

                    # 优先从HTML提取，如果没有则从文本提取
                    if html_content:
                        codes = self.extractor.extract_from_html(html_content)
                    elif text_content:
                        codes = self.extractor.extract_from_text(text_content)
                    else:
                        codes = []
                else:
                    # 如果获取详情失败，尝试从邮件列表数据提取
                    codes = self.extractor.extract_from_email(mail)
            else:
                # 如果没有mail_id，从邮件列表数据提取
                codes = self.extractor.extract_from_email(mail)

            all_codes.extend(codes)

            # 添加到显示内容
            display_content.append(f"📧 邮件 {i}: {subject}")
            if codes:
                display_content.append(f"🔑 验证码: {', '.join([code['code'] for code in codes])}")
            display_content.append("─" * 50)

            # 如果已经找到验证码，可以提前结束
            if all_codes:
                self.main_page.update_status(f"✅ 已找到验证码，停止处理")
                break
        
        # 更新显示
        if all_codes:
            # 按置信度排序，取置信度最高的验证码
            all_codes.sort(key=lambda x: x['confidence'], reverse=True)
            best_code = all_codes[0]

            display_content.insert(0, "🔑 Augment Code 激活额度验证码:")
            display_content.insert(1, "")
            display_content.insert(2, f"✅ {best_code['code']} (置信度: {best_code['confidence']:.1%})")
            display_content.insert(3, "")
            display_content.insert(4, "=" * 50)
            display_content.insert(5, "")

            # 只显示置信度最高的验证码
            codes_text = f"✅ {best_code['code']}"
            self.main_page.update_codes_display(codes_text)
        else:
            self.main_page.update_codes_display("❌ 未找到激活额度验证码\n\n🔍 可能原因:\n• 邮件中没有包含验证码\n• 验证码格式不被识别\n• 邮件内容为空或加载失败\n\n💡 建议:\n• 检查邮件是否为激活额度邮件\n• 稍后重试获取额度")
        
        self.main_page.update_emails_display("\n".join(display_content))
    
    def on_fetch_error(self, error_message: str):
        """处理获取错误"""
        self.main_page.update_status("❌ 获取失败")
        
        msg = QMessageBox()
        msg.setIcon(QMessageBox.Critical)
        msg.setWindowTitle("获取失败")
        msg.setText(f"获取激活额度时发生错误:\n\n{error_message}")
        msg.exec()
    
    def get_stylesheet(self):
        """获取样式表"""
        return """
            QMainWindow {
                background-color: #2B2B2B;
                color: #E0E0E0;
            }
            QGroupBox {
                font-weight: bold;
                border: 1px solid #555;
                border-radius: 6px;
                margin-top: 6px;
                padding-top: 6px;
                background-color: #333;
                color: #E0E0E0;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 8px;
                padding: 0 6px 0 6px;
                color: #4A90E2;
                font-size: 11px;
            }
            QLineEdit {
                background-color: #3A3A3A;
                border: 1px solid #555;
                border-radius: 4px;
                padding: 5px 6px;
                color: #E0E0E0;
                font-size: 11px;
            }
            QLineEdit:focus {
                border-color: #4A90E2;
            }
            QPushButton {
                background-color: #4A90E2;
                color: white;
                border: none;
                padding: 6px 12px;
                border-radius: 4px;
                font-weight: bold;
                font-size: 11px;
            }
            QPushButton:hover {
                background-color: #357ABD;
            }
            QPushButton:pressed {
                background-color: #2E6DA4;
            }
            QPushButton:disabled {
                background-color: #666;
                color: #999;
            }
            QTextEdit {
                background-color: #3A3A3A;
                border: 1px solid #555;
                border-radius: 4px;
                color: #E0E0E0;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 10px;
                padding: 6px;
            }
            QProgressBar {
                border: 1px solid #555;
                border-radius: 4px;
                text-align: center;
                background-color: #3A3A3A;
                color: #E0E0E0;
            }
            QProgressBar::chunk {
                background-color: #4A90E2;
                border-radius: 3px;
            }
            QStatusBar {
                background-color: #333;
                color: #E0E0E0;
                border-top: 1px solid #555;
            }
        """
