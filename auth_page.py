"""
授权页面组件
"""

from PySide6.QtWidgets import QWidget, QVBoxLayout, QLabel, QLineEdit, QPushButton
from PySide6.QtCore import Qt, Signal
import config


class AuthPage(QWidget):
    """授权验证页面"""
    
    # 信号：授权成功
    auth_success = Signal()
    
    def __init__(self):
        super().__init__()
        self.setup_ui()
    
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(30, 40, 30, 40)
        
        # 标题
        title_label = QLabel("🔐 Augment Code")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: #4A90E2;
                margin-bottom: 5px;
                padding: 5px;
            }
        """)
        
        # 副标题
        subtitle_label = QLabel("激活额度获取工具")
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #AAA;
                margin-bottom: 20px;
                padding: 3px;
            }
        """)
        
        # 授权说明
        info_label = QLabel("🛡️ 请输入授权码以继续使用")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("""
            QLabel {
                font-size: 13px;
                color: #E0E0E0;
                margin-bottom: 15px;
                padding: 3px;
            }
        """)
        
        # 授权码输入框
        self.auth_input = QLineEdit()
        self.auth_input.setPlaceholderText("请输入6位授权码")
        self.auth_input.setMaxLength(6)
        self.auth_input.setAlignment(Qt.AlignCenter)
        self.auth_input.setStyleSheet("""
            QLineEdit {
                background-color: #3A3A3A;
                border: 2px solid #555;
                border-radius: 6px;
                padding: 10px 14px;
                color: #E0E0E0;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 14px;
                font-weight: bold;
                letter-spacing: 1px;
            }
            QLineEdit:focus {
                border-color: #4A90E2;
            }
        """)
        self.auth_input.returnPressed.connect(self.verify_auth_code)
        
        # 验证按钮
        self.auth_button = QPushButton("🚀 验证授权")
        self.auth_button.clicked.connect(self.verify_auth_code)
        self.auth_button.setStyleSheet("""
            QPushButton {
                background-color: #4A90E2;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #357ABD;
            }
            QPushButton:pressed {
                background-color: #2E6DA4;
            }
        """)
        
        # 错误提示标签
        self.error_label = QLabel("")
        self.error_label.setAlignment(Qt.AlignCenter)
        self.error_label.setStyleSheet("""
            QLabel {
                color: #F44336;
                font-size: 11px;
                margin-top: 8px;
                padding: 2px;
            }
        """)
        self.error_label.hide()
        
        # 添加到布局
        layout.addStretch()
        layout.addWidget(title_label)
        layout.addWidget(subtitle_label)
        layout.addWidget(info_label)
        layout.addWidget(self.auth_input)
        layout.addWidget(self.auth_button)
        layout.addWidget(self.error_label)
        layout.addStretch()
    
    def verify_auth_code(self):
        """验证授权码"""
        entered_code = self.auth_input.text().strip()
        
        if entered_code == config.AUTH_CODE:
            # 授权成功
            self.auth_success.emit()
            self.auth_input.clear()
            self.error_label.hide()
        else:
            # 授权失败，显示错误信息
            self.error_label.setText("❌ 授权码错误，请重试")
            self.error_label.show()
            self.auth_input.clear()
            self.auth_input.setFocus()
    
    def reset(self):
        """重置页面状态"""
        self.auth_input.clear()
        self.error_label.hide()
        self.auth_input.setFocus()
