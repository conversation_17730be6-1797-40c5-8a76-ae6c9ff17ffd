# TempMail验证码获取工具 - 打包使用说明

## 🎉 打包成功！

你的应用程序已经成功打包为独立的可执行文件，可以在没有安装Python的电脑上直接运行。

## 📁 生成的文件

### Windows版本
- **文件位置**: `dist/TempMailTool.exe`
- **文件大小**: 约 50MB
- **运行方式**: 双击直接运行
- **系统要求**: Windows 7/8/10/11

### macOS版本（如果需要）
- **文件位置**: `dist/TempMailTool.app`
- **运行方式**: 双击直接运行
- **系统要求**: macOS 10.12+

## 🚀 如何使用

### Windows用户
1. 将 `dist/TempMailTool.exe` 复制到任何位置
2. 双击运行即可
3. 首次运行可能需要3-5秒启动时间
4. 如果Windows Defender提示安全警告，选择"仍要运行"

### macOS用户
1. 将 `dist/TempMailTool.app` 复制到应用程序文件夹或任何位置
2. 双击运行
3. 如果系统提示"来自未知开发者"，右键点击选择"打开"
4. 或在系统偏好设置 > 安全性与隐私中允许运行

## 📦 分发说明

### 单文件分发
- Windows: 直接分发 `TempMailTool.exe` 文件
- macOS: 分发整个 `TempMailTool.app` 文件夹

### 创建安装包（可选）
- Windows: 可以使用NSIS、Inno Setup等工具创建安装程序
- macOS: 可以创建DMG文件（运行macOS打包脚本时会询问）

## 🔧 重新打包

如果需要修改代码后重新打包：

### 简单打包（推荐）
```bash
# Windows
build_simple.bat

# macOS
./build_simple_macos.sh
```

### 手动打包
```bash
# 清理旧文件
rm -rf dist build  # macOS/Linux
rmdir /s /q dist build  # Windows

# 重新打包
pyinstaller --onefile --windowed --name="TempMailTool" --icon=icon.png main.py
```

## ⚠️ 注意事项

### 启动时间
- 首次启动需要3-5秒（PyInstaller应用的正常现象）
- 后续启动会更快

### 文件大小
- 单文件包含了所有依赖，所以体积较大（约50MB）
- 这是正常的，确保了应用的独立性

### 安全软件
- 某些杀毒软件可能误报，这是PyInstaller打包应用的常见问题
- 可以将应用添加到白名单

### 系统兼容性
- Windows: 支持Windows 7及以上版本
- macOS: 支持macOS 10.12及以上版本
- 在目标系统上测试运行以确保兼容性

## 🐛 常见问题

### Q: 应用启动很慢
A: 这是PyInstaller的正常现象，首次启动需要解压临时文件

### Q: Windows提示病毒警告
A: 这是误报，可以添加到杀毒软件白名单

### Q: macOS提示安全警告
A: 右键点击应用，选择"打开"，或在系统偏好设置中允许

### Q: 应用无法启动
A: 检查系统是否满足最低要求，尝试在命令行运行查看错误信息

### Q: 想要更小的文件
A: 可以使用 `--onedir` 模式，但会生成文件夹而不是单文件

## 📝 技术细节

- **打包工具**: PyInstaller 5.13.2
- **打包模式**: 单文件模式 (--onefile)
- **GUI模式**: 无控制台窗口 (--windowed)
- **图标**: 自动转换PNG为ICO格式
- **依赖**: 所有Python依赖都已内置

## 🎯 下一步

1. **测试**: 在不同的电脑上测试应用运行
2. **分发**: 将exe文件分享给用户
3. **反馈**: 收集用户反馈并改进
4. **更新**: 修改代码后重新打包发布

---

**恭喜！你的应用程序已经可以独立运行了！** 🎉
