"""
应用程序路径管理模块
处理应用程序数据文件的存储路径，确保打包后的应用程序将文件存储在合适的位置
"""

import os
import sys
import tempfile
from pathlib import Path
import logging

logger = logging.getLogger(__name__)


def get_app_data_dir() -> str:
    """
    获取应用程序数据目录
    
    开发环境：使用当前目录
    打包环境：使用系统临时目录下的应用程序专用文件夹
    
    Returns:
        str: 应用程序数据目录路径
    """
    app_name = "TempMailTool"
    
    # 检查是否为打包后的应用程序
    if getattr(sys, 'frozen', False):
        # 打包后的应用程序，使用临时目录
        if sys.platform.startswith('win'):
            # Windows: 使用 %TEMP%\TempMailTool
            base_dir = os.environ.get('TEMP', tempfile.gettempdir())
        elif sys.platform.startswith('darwin'):
            # macOS: 使用 ~/Library/Caches/TempMailTool
            base_dir = os.path.expanduser('~/Library/Caches')
        else:
            # Linux: 使用 ~/.cache/TempMailTool
            base_dir = os.path.expanduser('~/.cache')
        
        app_data_dir = os.path.join(base_dir, app_name)
    else:
        # 开发环境，使用当前目录
        app_data_dir = os.getcwd()
    
    # 确保目录存在
    try:
        os.makedirs(app_data_dir, exist_ok=True)
        logger.info(f"应用程序数据目录: {app_data_dir}")
    except Exception as e:
        logger.error(f"创建应用程序数据目录失败: {e}")
        # 如果创建失败，回退到临时目录
        app_data_dir = tempfile.gettempdir()
        logger.warning(f"回退到临时目录: {app_data_dir}")
    
    return app_data_dir


def get_app_file_path(filename: str) -> str:
    """
    获取应用程序文件的完整路径
    
    Args:
        filename (str): 文件名
        
    Returns:
        str: 文件的完整路径
    """
    app_data_dir = get_app_data_dir()
    return os.path.join(app_data_dir, filename)


def get_log_file_path() -> str:
    """获取日志文件路径"""
    return get_app_file_path("tempmail_tool.log")


def get_auth_file_path() -> str:
    """获取授权文件路径"""
    return get_app_file_path("auth_status.json")


def get_usage_file_path() -> str:
    """获取使用数据文件路径"""
    return get_app_file_path("usage_data.json")


def get_resource_path(relative_path: str) -> str:
    """
    获取资源文件路径（如图标等）
    
    Args:
        relative_path (str): 相对路径
        
    Returns:
        str: 资源文件的完整路径
    """
    if getattr(sys, 'frozen', False):
        # 打包后的应用程序
        if hasattr(sys, '_MEIPASS'):
            # PyInstaller 临时目录
            base_path = sys._MEIPASS
        else:
            # 其他打包工具
            base_path = os.path.dirname(sys.executable)
    else:
        # 开发环境
        base_path = os.path.dirname(os.path.abspath(__file__))
    
    return os.path.join(base_path, relative_path)


def cleanup_old_files():
    """清理旧的数据文件（可选）"""
    try:
        # 如果是打包环境，清理当前目录下的旧文件
        if getattr(sys, 'frozen', False):
            current_dir = os.path.dirname(sys.executable)
            old_files = [
                "tempmail_tool.log",
                "auth_status.json", 
                "usage_data.json"
            ]
            
            for filename in old_files:
                old_file_path = os.path.join(current_dir, filename)
                if os.path.exists(old_file_path):
                    try:
                        os.remove(old_file_path)
                        logger.info(f"已清理旧文件: {old_file_path}")
                    except Exception as e:
                        logger.warning(f"清理旧文件失败 {old_file_path}: {e}")
    except Exception as e:
        logger.error(f"清理旧文件时出错: {e}")


def get_app_info():
    """获取应用程序路径信息（用于调试）"""
    info = {
        "is_frozen": getattr(sys, 'frozen', False),
        "executable": sys.executable,
        "app_data_dir": get_app_data_dir(),
        "log_file": get_log_file_path(),
        "auth_file": get_auth_file_path(),
        "usage_file": get_usage_file_path(),
    }
    
    if hasattr(sys, '_MEIPASS'):
        info["meipass"] = sys._MEIPASS
    
    return info
