# Augment Code - 激活额度获取工具

## 🚀 功能介绍

这是一个专为 Augment Code 用户设计的激活额度获取工具，具有现代化的深色主题界面和授权验证系统，紧凑而美观。

## ✨ 主要特性

- 🎨 **现代深色主题** - 符合 Augment Code 风格的专业界面
- 🔐 **授权验证系统** - 输入授权码111111才能使用工具
- 🎲 **随机邮箱生成器** - 一键生成 @aicgbot.com 后缀的展示邮箱
- 🔑 **智能验证码提取** - 自动识别和提取激活额度验证码
- 📊 **实时状态显示** - 清晰的进度指示和状态反馈
- 🚫 **使用次数限制** - 每日50次使用限制，防止滥用
- 💾 **本地数据存储** - 使用记录本地保存，无需联网验证

## 🖥️ 界面说明

### 主要区域

1. **🔧 Augment Code 激活邮箱**
   - 显示随机生成的展示邮箱地址
   - 🎲 按钮：生成新的随机邮箱
   - 🚀 获取激活邮件按钮

2. **📊 获取状态**
   - 实时显示当前操作状态
   - 进度条显示获取进度

3. **🔑 激活验证码**
   - 显示提取到的验证码
   - 按信心度排序，最高的在前
   - 支持文本选择和复制

4. **📧 邮件详情**
   - 显示邮件的详细内容
   - 包含发件人、主题、时间等信息

## 🔧 使用方法

1. **启动应用**
   ```bash
   python main.py
   ```

2. **生成展示邮箱**
   - 应用启动时会自动生成一个随机邮箱地址
   - 点击 🎲 按钮可生成新的随机邮箱

3. **获取激活邮件**
   - 点击 "🚀 获取激活邮件" 按钮
   - 等待系统获取并分析邮件内容

4. **复制验证码**
   - 在 "🔑 激活验证码" 区域查看提取的验证码
   - 选择信心度最高的验证码进行复制使用

## ⚙️ 技术特性

- **界面尺寸**: 520×420 像素，紧凑而功能完整
- **主题风格**: 深色专业主题，护眼且美观
- **响应式设计**: 支持高DPI显示器
- **异步处理**: 邮件获取不阻塞界面操作
- **错误处理**: 完善的错误提示和处理机制

## 📝 注意事项

- **展示邮箱**: 界面显示的邮箱仅用于展示，实际获取使用内置配置的真实邮箱
- **使用限制**: 每日最多使用50次，合理使用资源
- **验证码格式**: 支持4-8位数字、字母数字组合等多种格式
- **网络要求**: 需要稳定的网络连接访问 tempmail.plus 服务

## 🛠️ 故障排除

### 常见问题

1. **无法获取邮件**
   - 检查网络连接
   - 确认邮件服务正常
   - 稍后重试

2. **未找到验证码**
   - 确认邮件是激活邮件
   - 检查邮件内容格式
   - 验证码可能在邮件附件中

3. **界面显示异常**
   - 重启应用程序
   - 检查系统字体设置
   - 确认PySide6版本兼容性

## 📋 系统要求

- Python 3.8+
- PySide6 6.5.0+
- Windows/macOS/Linux
- 网络连接

## 🔄 更新日志

### v1.0.0
- ✅ 实现基础邮件获取功能
- ✅ 添加验证码自动提取
- ✅ 设计现代化深色主题界面
- ✅ 集成随机邮箱生成器
- ✅ 添加使用次数限制
- ✅ 完善错误处理机制

---

**开发者**: Augment Code Team  
**版本**: 1.0.0  
**更新时间**: 2025-07-31
