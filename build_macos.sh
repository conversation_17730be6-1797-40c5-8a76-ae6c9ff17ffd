#!/bin/bash

# TempMail验证码获取工具 - macOS打包脚本

set -e  # 遇到错误立即退出

echo "========================================"
echo "TempMail验证码获取工具 - macOS打包脚本"
echo "========================================"
echo

# 设置变量
APP_NAME="TempMail验证码获取工具"
BUILD_DIR="dist"
SPEC_FILE="tempmail_tool.spec"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Python环境
print_info "[1/7] 检查Python环境..."
if ! command -v python3 &> /dev/null; then
    print_error "未找到Python3环境，请确保Python3已安装"
    exit 1
fi

PYTHON_VERSION=$(python3 --version)
print_success "Python环境检查通过: $PYTHON_VERSION"

# 检查并安装PyInstaller
print_info "[2/7] 检查PyInstaller..."
if ! python3 -c "import PyInstaller" &> /dev/null; then
    print_info "正在安装PyInstaller..."
    pip3 install pyinstaller
    if [ $? -ne 0 ]; then
        print_error "PyInstaller安装失败"
        exit 1
    fi
else
    print_success "PyInstaller已安装"
fi

# 安装项目依赖
print_info "[3/7] 安装项目依赖..."
if [ -f "requirements.txt" ]; then
    pip3 install -r requirements.txt
    if [ $? -ne 0 ]; then
        print_warning "部分依赖安装失败，继续打包..."
    fi
else
    print_warning "未找到requirements.txt文件"
fi

# 清理之前的构建
print_info "[4/7] 清理之前的构建..."
if [ -d "$BUILD_DIR" ]; then
    print_info "删除旧的构建目录..."
    rm -rf "$BUILD_DIR"
fi
if [ -d "build" ]; then
    print_info "删除临时构建目录..."
    rm -rf "build"
fi

# 准备资源文件
print_info "[5/7] 准备资源文件..."
if [ ! -f "icon.png" ] && [ ! -f "icon.ico" ]; then
    print_warning "未找到应用程序图标文件"
fi

# 检查必要文件
print_info "[6/7] 检查必要文件..."
if [ ! -f "main.py" ]; then
    print_error "未找到main.py文件"
    exit 1
fi

if [ ! -f "$SPEC_FILE" ]; then
    print_error "未找到$SPEC_FILE文件"
    exit 1
fi

# 执行打包
print_info "[7/7] 开始打包应用程序..."
echo "这可能需要几分钟时间，请耐心等待..."
echo

pyinstaller --clean "$SPEC_FILE"

# 检查打包结果
if [ $? -ne 0 ]; then
    echo
    print_error "========================================"
    print_error "打包失败！"
    print_error "========================================"
    print_error "请检查上面的错误信息并修复问题后重试"
    exit 1
fi

# 检查输出文件
APP_PATH="$BUILD_DIR/$APP_NAME.app"
if [ -d "$APP_PATH" ]; then
    echo
    print_success "========================================"
    print_success "打包成功！"
    print_success "========================================"
    print_success "应用程序位置: $APP_PATH"
    
    # 显示文件大小
    APP_SIZE=$(du -sh "$APP_PATH" | cut -f1)
    print_info "应用程序大小: $APP_SIZE"
    
    echo
    print_info "提示:"
    print_info "1. 可以直接双击运行 $APP_PATH"
    print_info "2. 该应用包含所有依赖，可以在其他macOS电脑上独立运行"
    print_info "3. 首次运行可能需要几秒钟启动时间"
    print_info "4. 如果系统提示安全警告，请在系统偏好设置中允许运行"
    echo
    
    # 询问是否立即测试
    read -p "是否立即测试应用程序？(y/n): " test_choice
    if [[ "$test_choice" =~ ^[Yy]$ ]]; then
        print_info "启动应用程序进行测试..."
        open "$APP_PATH"
    fi
    
    # 询问是否创建DMG安装包
    echo
    read -p "是否创建DMG安装包？(y/n): " dmg_choice
    if [[ "$dmg_choice" =~ ^[Yy]$ ]]; then
        print_info "创建DMG安装包..."
        DMG_NAME="${APP_NAME}_v1.0.0.dmg"
        
        # 创建临时目录
        TEMP_DMG_DIR="temp_dmg"
        mkdir -p "$TEMP_DMG_DIR"
        
        # 复制应用程序到临时目录
        cp -R "$APP_PATH" "$TEMP_DMG_DIR/"
        
        # 创建DMG
        hdiutil create -volname "$APP_NAME" -srcfolder "$TEMP_DMG_DIR" -ov -format UDZO "$DMG_NAME"
        
        # 清理临时目录
        rm -rf "$TEMP_DMG_DIR"
        
        if [ -f "$DMG_NAME" ]; then
            print_success "DMG安装包创建成功: $DMG_NAME"
        else
            print_warning "DMG安装包创建失败"
        fi
    fi
    
else
    echo
    print_error "========================================"
    print_error "打包可能失败！"
    print_error "========================================"
    print_error "未在 $BUILD_DIR 目录中找到应用程序包"
    print_error "请检查上面的输出信息"
    exit 1
fi

echo
print_success "打包脚本执行完成"
