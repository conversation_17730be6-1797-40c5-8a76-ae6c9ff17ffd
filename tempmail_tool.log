2025-07-31 14:34:41,864 - __main__ - INFO - ==================================================
2025-07-31 14:34:41,864 - __main__ - INFO - TempMail 验证码获取工具启动
2025-07-31 14:34:41,864 - __main__ - INFO - ==================================================
2025-07-31 14:34:41,900 - __main__ - INFO - 创建主窗口...
2025-07-31 14:34:42,000 - __main__ - CRITICAL - 启动失败: type object 'datetime.datetime' has no attribute 'timedelta'
2025-07-31 14:34:42,000 - __main__ - CRITICAL - Traceback (most recent call last):
  File "E:\ai编程开发\augument-mail-plugin\main.py", line 132, in main
    main_window = MainWindow()
                  ^^^^^^^^^^^^
  File "E:\ai编程开发\augument-mail-plugin\main_window.py", line 80, in __init__
    self.update_usage_display()
  File "E:\ai编程开发\augument-mail-plugin\main_window.py", line 262, in update_usage_display
    stats = self.usage_limiter.get_usage_stats()
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "E:\ai编程开发\augument-mail-plugin\usage_limiter.py", line 146, in get_usage_stats
    check_date = today - datetime.timedelta(days=i)
                         ^^^^^^^^^^^^^^^^^^
AttributeError: type object 'datetime.datetime' has no attribute 'timedelta'

2025-07-31 14:36:27,076 - __main__ - INFO - ==================================================
2025-07-31 14:36:27,076 - __main__ - INFO - TempMail 验证码获取工具启动
2025-07-31 14:36:27,076 - __main__ - INFO - ==================================================
2025-07-31 14:36:27,097 - __main__ - INFO - 创建主窗口...
2025-07-31 14:36:27,596 - __main__ - INFO - 主窗口已显示
2025-07-31 14:36:27,596 - __main__ - INFO - 应用程序配置:
2025-07-31 14:36:27,596 - __main__ - INFO -   - 窗口大小: 800x600
2025-07-31 14:36:27,596 - __main__ - INFO -   - 每日使用限制: 50
2025-07-31 14:36:27,597 - __main__ - INFO -   - 请求超时: 10秒
2025-07-31 14:36:27,597 - __main__ - INFO -   - 日志级别: INFO
2025-07-31 14:36:27,597 - __main__ - INFO - 应用程序开始运行...
2025-07-31 14:37:32,257 - __main__ - INFO - ==================================================
2025-07-31 14:37:32,257 - __main__ - INFO - TempMail 验证码获取工具启动
2025-07-31 14:37:32,257 - __main__ - INFO - ==================================================
2025-07-31 14:37:32,278 - __main__ - INFO - 创建主窗口...
2025-07-31 14:37:32,845 - __main__ - INFO - 主窗口已显示
2025-07-31 14:37:32,845 - __main__ - INFO - 应用程序配置:
2025-07-31 14:37:32,845 - __main__ - INFO -   - 窗口大小: 800x600
2025-07-31 14:37:32,845 - __main__ - INFO -   - 每日使用限制: 50
2025-07-31 14:37:32,846 - __main__ - INFO -   - 请求超时: 10秒
2025-07-31 14:37:32,846 - __main__ - INFO -   - 日志级别: INFO
2025-07-31 14:37:32,846 - __main__ - INFO - 应用程序开始运行...
2025-07-31 14:38:02,632 - usage_limiter - INFO - 记录使用: <EMAIL> - get_emails, 今日已使用: 1/50
2025-07-31 14:38:02,637 - tempmail_api - INFO - 正在获取邮箱 <EMAIL> 的邮件...
2025-07-31 14:38:04,247 - tempmail_api - INFO - 成功获取 0 封邮件
2025-07-31 14:41:02,485 - __main__ - INFO - 应用程序退出，退出码: 0
2025-07-31 14:46:09,965 - __main__ - INFO - ==================================================
2025-07-31 14:46:09,965 - __main__ - INFO - TempMail 验证码获取工具启动
2025-07-31 14:46:09,965 - __main__ - INFO - ==================================================
2025-07-31 14:46:09,983 - __main__ - INFO - 创建主窗口...
2025-07-31 14:46:09,983 - usage_limiter - INFO - 已加载使用数据: 1 条记录
2025-07-31 14:46:10,726 - __main__ - INFO - 主窗口已显示
2025-07-31 14:46:10,727 - __main__ - INFO - 应用程序配置:
2025-07-31 14:46:10,727 - __main__ - INFO -   - 窗口大小: 800x600
2025-07-31 14:46:10,727 - __main__ - INFO -   - 每日使用限制: 50
2025-07-31 14:46:10,727 - __main__ - INFO -   - 请求超时: 10秒
2025-07-31 14:46:10,727 - __main__ - INFO -   - 日志级别: INFO
2025-07-31 14:46:10,727 - __main__ - INFO - 应用程序开始运行...
2025-07-31 14:46:32,583 - usage_limiter - INFO - 记录使用: <EMAIL> - get_emails, 今日已使用: 2/50
2025-07-31 14:46:32,587 - tempmail_api - INFO - 正在获取邮箱 <EMAIL> 的邮件...
2025-07-31 14:46:37,597 - tempmail_api - ERROR - 请求失败: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-31 14:46:51,632 - usage_limiter - INFO - 记录使用: <EMAIL> - get_emails, 今日已使用: 3/50
2025-07-31 14:46:51,635 - tempmail_api - INFO - 正在获取邮箱 <EMAIL> 的邮件...
2025-07-31 14:46:52,787 - tempmail_api - INFO - 成功获取 0 封邮件
2025-07-31 14:46:58,386 - usage_limiter - INFO - 记录使用: <EMAIL> - get_emails, 今日已使用: 4/50
2025-07-31 14:46:58,389 - tempmail_api - INFO - 正在获取邮箱 <EMAIL> 的邮件...
2025-07-31 14:46:59,893 - tempmail_api - INFO - 成功获取 0 封邮件
2025-07-31 14:47:04,521 - usage_limiter - INFO - 记录使用: <EMAIL> - get_emails, 今日已使用: 5/50
2025-07-31 14:47:04,525 - tempmail_api - INFO - 正在获取邮箱 <EMAIL> 的邮件...
2025-07-31 14:47:09,120 - tempmail_api - INFO - 成功获取 0 封邮件
2025-07-31 14:56:35,261 - __main__ - INFO - ==================================================
2025-07-31 14:56:35,261 - __main__ - INFO - TempMail 验证码获取工具启动
2025-07-31 14:56:35,261 - __main__ - INFO - ==================================================
2025-07-31 14:56:35,281 - __main__ - INFO - 创建主窗口...
2025-07-31 14:56:35,281 - usage_limiter - INFO - 已加载使用数据: 1 条记录
2025-07-31 14:56:36,104 - __main__ - INFO - 主窗口已显示
2025-07-31 14:56:36,104 - __main__ - INFO - 应用程序配置:
2025-07-31 14:56:36,105 - __main__ - INFO -   - 窗口大小: 520x420
2025-07-31 14:56:36,105 - __main__ - INFO -   - 每日使用限制: 50
2025-07-31 14:56:36,105 - __main__ - INFO -   - 请求超时: 10秒
2025-07-31 14:56:36,105 - __main__ - INFO -   - 日志级别: INFO
2025-07-31 14:56:36,105 - __main__ - INFO - 应用程序开始运行...
2025-07-31 14:57:03,097 - usage_limiter - INFO - 记录使用: <EMAIL> - get_emails, 今日已使用: 6/50
2025-07-31 14:57:03,102 - tempmail_api - INFO - 正在获取邮箱 <EMAIL> 的邮件...
2025-07-31 14:57:05,902 - tempmail_api - INFO - 成功获取 0 封邮件
2025-07-31 14:58:29,590 - __main__ - INFO - ==================================================
2025-07-31 14:58:29,591 - __main__ - INFO - TempMail 验证码获取工具启动
2025-07-31 14:58:29,591 - __main__ - INFO - ==================================================
2025-07-31 14:58:29,643 - __main__ - INFO - 创建主窗口...
2025-07-31 14:58:29,644 - usage_limiter - INFO - 已加载使用数据: 1 条记录
2025-07-31 14:58:30,452 - __main__ - INFO - 主窗口已显示
2025-07-31 14:58:30,452 - __main__ - INFO - 应用程序配置:
2025-07-31 14:58:30,452 - __main__ - INFO -   - 窗口大小: 520x420
2025-07-31 14:58:30,452 - __main__ - INFO -   - 每日使用限制: 50
2025-07-31 14:58:30,453 - __main__ - INFO -   - 请求超时: 10秒
2025-07-31 14:58:30,453 - __main__ - INFO -   - 日志级别: INFO
2025-07-31 14:58:30,453 - __main__ - INFO - 应用程序开始运行...
2025-07-31 15:01:53,066 - __main__ - INFO - 应用程序退出，退出码: 0
2025-07-31 15:05:03,621 - usage_limiter - INFO - 记录使用: <EMAIL> - get_emails, 今日已使用: 6/50
2025-07-31 15:05:03,623 - tempmail_api - INFO - 正在获取邮箱 <EMAIL> 的邮件...
2025-07-31 15:05:05,817 - tempmail_api - INFO - 成功获取 0 封邮件
2025-07-31 15:05:07,043 - usage_limiter - INFO - 记录使用: <EMAIL> - get_emails, 今日已使用: 7/50
2025-07-31 15:05:07,046 - tempmail_api - INFO - 正在获取邮箱 <EMAIL> 的邮件...
2025-07-31 15:05:08,637 - tempmail_api - INFO - 成功获取 0 封邮件
2025-07-31 15:05:09,379 - usage_limiter - INFO - 记录使用: <EMAIL> - get_emails, 今日已使用: 8/50
2025-07-31 15:05:09,384 - tempmail_api - INFO - 正在获取邮箱 <EMAIL> 的邮件...
2025-07-31 15:05:14,400 - tempmail_api - ERROR - 请求失败: ('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))
2025-07-31 15:06:01,112 - usage_limiter - INFO - 记录使用: <EMAIL> - get_emails, 今日已使用: 9/50
2025-07-31 15:06:01,117 - tempmail_api - INFO - 正在获取邮箱 <EMAIL> 的邮件...
2025-07-31 15:06:02,546 - tempmail_api - INFO - 成功获取 0 封邮件
2025-07-31 15:06:31,931 - __main__ - INFO - 应用程序退出，退出码: 0
2025-07-31 15:06:35,583 - __main__ - INFO - ==================================================
2025-07-31 15:06:35,584 - __main__ - INFO - TempMail 验证码获取工具启动
2025-07-31 15:06:35,584 - __main__ - INFO - ==================================================
2025-07-31 15:06:35,613 - __main__ - INFO - 创建主窗口...
2025-07-31 15:06:35,614 - usage_limiter - INFO - 已加载使用数据: 1 条记录
2025-07-31 15:06:36,379 - __main__ - INFO - 主窗口已显示
2025-07-31 15:06:36,379 - __main__ - INFO - 应用程序配置:
2025-07-31 15:06:36,380 - __main__ - INFO -   - 窗口大小: 520x420
2025-07-31 15:06:36,380 - __main__ - INFO -   - 每日使用限制: 50
2025-07-31 15:06:36,380 - __main__ - INFO -   - 请求超时: 10秒
2025-07-31 15:06:36,380 - __main__ - INFO -   - 日志级别: INFO
2025-07-31 15:06:36,381 - __main__ - INFO - 应用程序开始运行...
2025-07-31 15:06:38,510 - usage_limiter - INFO - 记录使用: <EMAIL> - get_emails, 今日已使用: 10/50
2025-07-31 15:06:38,513 - tempmail_api - INFO - 正在获取邮箱 <EMAIL> 的邮件...
2025-07-31 15:06:43,304 - tempmail_api - INFO - 成功获取 0 封邮件
2025-07-31 15:06:45,922 - usage_limiter - INFO - 记录使用: <EMAIL> - get_emails, 今日已使用: 11/50
2025-07-31 15:06:45,927 - tempmail_api - INFO - 正在获取邮箱 <EMAIL> 的邮件...
2025-07-31 15:06:47,824 - tempmail_api - INFO - 成功获取 0 封邮件
2025-07-31 15:06:50,170 - usage_limiter - INFO - 记录使用: <EMAIL> - get_emails, 今日已使用: 12/50
2025-07-31 15:06:50,173 - tempmail_api - INFO - 正在获取邮箱 <EMAIL> 的邮件...
2025-07-31 15:06:52,296 - tempmail_api - INFO - 成功获取 0 封邮件
2025-07-31 15:07:05,998 - usage_limiter - INFO - 记录使用: <EMAIL> - get_emails, 今日已使用: 13/50
2025-07-31 15:07:06,001 - tempmail_api - INFO - 正在获取邮箱 <EMAIL> 的邮件...
2025-07-31 15:07:07,172 - tempmail_api - INFO - 成功获取 0 封邮件
2025-07-31 15:07:09,590 - usage_limiter - INFO - 记录使用: <EMAIL> - get_emails, 今日已使用: 14/50
2025-07-31 15:07:09,594 - tempmail_api - INFO - 正在获取邮箱 <EMAIL> 的邮件...
2025-07-31 15:07:10,856 - tempmail_api - INFO - 成功获取 0 封邮件
2025-07-31 15:07:25,890 - usage_limiter - INFO - 记录使用: <EMAIL> - get_emails, 今日已使用: 15/50
2025-07-31 15:07:25,893 - tempmail_api - INFO - 正在获取邮箱 <EMAIL> 的邮件...
2025-07-31 15:07:28,310 - tempmail_api - INFO - 成功获取 0 封邮件
2025-07-31 15:13:50,026 - __main__ - INFO - 应用程序退出，退出码: 0
2025-07-31 15:13:52,243 - __main__ - INFO - ==================================================
2025-07-31 15:13:52,243 - __main__ - INFO - TempMail 验证码获取工具启动
2025-07-31 15:13:52,244 - __main__ - INFO - ==================================================
2025-07-31 15:13:52,273 - __main__ - INFO - 创建主窗口...
2025-07-31 15:13:52,273 - usage_limiter - INFO - 已加载使用数据: 1 条记录
2025-07-31 15:13:53,261 - __main__ - INFO - 主窗口已显示
2025-07-31 15:13:53,261 - __main__ - INFO - 应用程序配置:
2025-07-31 15:13:53,262 - __main__ - INFO -   - 窗口大小: 520x420
2025-07-31 15:13:53,262 - __main__ - INFO -   - 每日使用限制: 50
2025-07-31 15:13:53,262 - __main__ - INFO -   - 请求超时: 10秒
2025-07-31 15:13:53,262 - __main__ - INFO -   - 日志级别: INFO
2025-07-31 15:13:53,263 - __main__ - INFO - 应用程序开始运行...
2025-07-31 15:13:59,761 - __main__ - INFO - 应用程序退出，退出码: 0
2025-07-31 15:15:34,638 - __main__ - INFO - ==================================================
2025-07-31 15:15:34,638 - __main__ - INFO - TempMail 验证码获取工具启动
2025-07-31 15:15:34,638 - __main__ - INFO - ==================================================
2025-07-31 15:15:34,663 - __main__ - INFO - 创建主窗口...
2025-07-31 15:15:34,663 - usage_limiter - INFO - 已加载使用数据: 1 条记录
2025-07-31 15:15:35,446 - __main__ - INFO - 主窗口已显示
2025-07-31 15:15:35,447 - __main__ - INFO - 应用程序配置:
2025-07-31 15:15:35,447 - __main__ - INFO -   - 窗口大小: 520x420
2025-07-31 15:15:35,447 - __main__ - INFO -   - 每日使用限制: 50
2025-07-31 15:15:35,447 - __main__ - INFO -   - 请求超时: 10秒
2025-07-31 15:15:35,447 - __main__ - INFO -   - 日志级别: INFO
2025-07-31 15:15:35,448 - __main__ - INFO - 应用程序开始运行...
2025-07-31 15:18:20,531 - __main__ - INFO - 应用程序退出，退出码: 0
2025-07-31 15:18:27,045 - __main__ - INFO - ==================================================
2025-07-31 15:18:27,045 - __main__ - INFO - TempMail 验证码获取工具启动
2025-07-31 15:18:27,045 - __main__ - INFO - ==================================================
2025-07-31 15:18:27,072 - __main__ - INFO - 创建主窗口...
2025-07-31 15:18:27,072 - usage_limiter - INFO - 已加载使用数据: 1 条记录
2025-07-31 15:18:28,060 - __main__ - INFO - 主窗口已显示
2025-07-31 15:18:28,060 - __main__ - INFO - 应用程序配置:
2025-07-31 15:18:28,060 - __main__ - INFO -   - 窗口大小: 520x420
2025-07-31 15:18:28,060 - __main__ - INFO -   - 每日使用限制: 50
2025-07-31 15:18:28,061 - __main__ - INFO -   - 请求超时: 10秒
2025-07-31 15:18:28,061 - __main__ - INFO -   - 日志级别: INFO
2025-07-31 15:18:28,061 - __main__ - INFO - 应用程序开始运行...
2025-07-31 15:19:09,386 - __main__ - INFO - 应用程序退出，退出码: 0
2025-07-31 15:20:50,260 - __main__ - INFO - ==================================================
2025-07-31 15:20:50,261 - __main__ - INFO - TempMail 验证码获取工具启动
2025-07-31 15:20:50,261 - __main__ - INFO - ==================================================
2025-07-31 15:20:50,288 - __main__ - INFO - 创建主窗口...
2025-07-31 15:20:50,289 - usage_limiter - INFO - 已加载使用数据: 1 条记录
2025-07-31 15:20:51,047 - __main__ - INFO - 主窗口已显示
2025-07-31 15:20:51,048 - __main__ - INFO - 应用程序配置:
2025-07-31 15:20:51,048 - __main__ - INFO -   - 窗口大小: 520x420
2025-07-31 15:20:51,048 - __main__ - INFO -   - 每日使用限制: 50
2025-07-31 15:20:51,048 - __main__ - INFO -   - 请求超时: 10秒
2025-07-31 15:20:51,048 - __main__ - INFO -   - 日志级别: INFO
2025-07-31 15:20:51,048 - __main__ - INFO - 应用程序开始运行...
2025-07-31 15:22:26,060 - __main__ - INFO - 应用程序退出，退出码: 0
2025-07-31 15:25:06,300 - __main__ - INFO - ==================================================
2025-07-31 15:25:06,300 - __main__ - INFO - TempMail 验证码获取工具启动
2025-07-31 15:25:06,300 - __main__ - INFO - ==================================================
2025-07-31 15:25:06,327 - __main__ - INFO - 创建主窗口...
2025-07-31 15:25:06,328 - usage_limiter - INFO - 已加载使用数据: 1 条记录
2025-07-31 15:25:06,849 - __main__ - INFO - 主窗口已显示
2025-07-31 15:25:06,849 - __main__ - INFO - 应用程序配置:
2025-07-31 15:25:06,849 - __main__ - INFO -   - 窗口大小: 520x420
2025-07-31 15:25:06,850 - __main__ - INFO -   - 每日使用限制: 50
2025-07-31 15:25:06,850 - __main__ - INFO -   - 请求超时: 10秒
2025-07-31 15:25:06,850 - __main__ - INFO -   - 日志级别: INFO
2025-07-31 15:25:06,850 - __main__ - INFO - 应用程序开始运行...
2025-07-31 15:26:04,956 - __main__ - INFO - 应用程序退出，退出码: 0
2025-07-31 15:38:49,472 - __main__ - INFO - ==================================================
2025-07-31 15:38:49,472 - __main__ - INFO - TempMail 验证码获取工具启动
2025-07-31 15:38:49,472 - __main__ - INFO - ==================================================
2025-07-31 15:38:49,499 - __main__ - INFO - 创建主窗口...
2025-07-31 15:38:49,500 - usage_limiter - INFO - 已加载使用数据: 1 条记录
2025-07-31 15:38:50,321 - __main__ - INFO - 主窗口已显示
2025-07-31 15:38:50,322 - __main__ - INFO - 应用程序配置:
2025-07-31 15:38:50,322 - __main__ - INFO -   - 窗口大小: 520x420
2025-07-31 15:38:50,322 - __main__ - INFO -   - 每日使用限制: 50
2025-07-31 15:38:50,322 - __main__ - INFO -   - 请求超时: 10秒
2025-07-31 15:38:50,322 - __main__ - INFO -   - 日志级别: INFO
2025-07-31 15:38:50,323 - __main__ - INFO - 应用程序开始运行...
2025-07-31 15:39:07,699 - __main__ - INFO - 应用程序退出，退出码: 0
2025-07-31 15:39:17,576 - __main__ - INFO - ==================================================
2025-07-31 15:39:17,577 - __main__ - INFO - TempMail 验证码获取工具启动
2025-07-31 15:39:17,577 - __main__ - INFO - ==================================================
2025-07-31 15:39:17,623 - __main__ - INFO - 创建主窗口...
2025-07-31 15:39:17,624 - usage_limiter - INFO - 已加载使用数据: 1 条记录
2025-07-31 15:39:18,460 - __main__ - INFO - 主窗口已显示
2025-07-31 15:39:18,461 - __main__ - INFO - 应用程序配置:
2025-07-31 15:39:18,461 - __main__ - INFO -   - 窗口大小: 520x420
2025-07-31 15:39:18,461 - __main__ - INFO -   - 每日使用限制: 50
2025-07-31 15:39:18,461 - __main__ - INFO -   - 请求超时: 10秒
2025-07-31 15:39:18,462 - __main__ - INFO -   - 日志级别: INFO
2025-07-31 15:39:18,462 - __main__ - INFO - 应用程序开始运行...
2025-07-31 15:41:28,080 - __main__ - INFO - 应用程序退出，退出码: 0
2025-07-31 15:42:40,996 - __main__ - INFO - ==================================================
2025-07-31 15:42:40,997 - __main__ - INFO - TempMail 验证码获取工具启动
2025-07-31 15:42:40,997 - __main__ - INFO - ==================================================
2025-07-31 15:42:41,024 - __main__ - INFO - 创建主窗口...
2025-07-31 15:42:41,024 - usage_limiter - INFO - 已加载使用数据: 1 条记录
2025-07-31 15:42:41,922 - __main__ - INFO - 主窗口已显示
2025-07-31 15:42:41,923 - __main__ - INFO - 应用程序配置:
2025-07-31 15:42:41,923 - __main__ - INFO -   - 窗口大小: 520x420
2025-07-31 15:42:41,923 - __main__ - INFO -   - 每日使用限制: 50
2025-07-31 15:42:41,923 - __main__ - INFO -   - 请求超时: 10秒
2025-07-31 15:42:41,923 - __main__ - INFO -   - 日志级别: INFO
2025-07-31 15:42:41,923 - __main__ - INFO - 应用程序开始运行...
2025-07-31 15:42:50,243 - usage_limiter - INFO - 记录使用: <EMAIL> - get_quota, 今日已使用: 16/50
2025-07-31 15:42:50,248 - tempmail_api - INFO - 正在获取邮箱 <EMAIL> 的邮件...
2025-07-31 15:42:52,272 - tempmail_api - INFO - 成功获取 0 封邮件
2025-07-31 15:50:06,585 - __main__ - INFO - ==================================================
2025-07-31 15:50:06,585 - __main__ - INFO - TempMail 验证码获取工具启动
2025-07-31 15:50:06,585 - __main__ - INFO - ==================================================
2025-07-31 15:50:06,610 - __main__ - INFO - 创建主窗口...
2025-07-31 15:50:06,611 - usage_limiter - INFO - 已加载使用数据: 1 条记录
2025-07-31 15:50:07,386 - __main__ - INFO - 主窗口已显示
2025-07-31 15:50:07,387 - __main__ - INFO - 应用程序配置:
2025-07-31 15:50:07,387 - __main__ - INFO -   - 窗口大小: 520x420
2025-07-31 15:50:07,387 - __main__ - INFO -   - 每日使用限制: 50
2025-07-31 15:50:07,387 - __main__ - INFO -   - 请求超时: 10秒
2025-07-31 15:50:07,387 - __main__ - INFO -   - 日志级别: INFO
2025-07-31 15:50:07,387 - __main__ - INFO - 应用程序开始运行...
2025-07-31 15:50:23,125 - usage_limiter - INFO - 记录使用: <EMAIL> - get_quota, 今日已使用: 17/50
2025-07-31 15:50:23,128 - root - ERROR - 获取邮件失败: 'TempMailAPI' object has no attribute 'get_mails'
2025-07-31 15:50:27,269 - usage_limiter - INFO - 记录使用: <EMAIL> - get_quota, 今日已使用: 18/50
2025-07-31 15:50:27,276 - root - ERROR - 获取邮件失败: 'TempMailAPI' object has no attribute 'get_mails'
2025-07-31 15:50:34,253 - usage_limiter - INFO - 记录使用: <EMAIL> - get_quota, 今日已使用: 19/50
2025-07-31 15:50:34,256 - root - ERROR - 获取邮件失败: 'TempMailAPI' object has no attribute 'get_mails'
2025-07-31 15:51:16,792 - usage_limiter - INFO - 记录使用: <EMAIL> - get_quota, 今日已使用: 20/50
2025-07-31 15:51:16,795 - root - ERROR - 获取邮件失败: 'TempMailAPI' object has no attribute 'get_mails'
2025-07-31 15:51:50,964 - usage_limiter - INFO - 记录使用: <EMAIL> - get_quota, 今日已使用: 21/50
2025-07-31 15:51:50,967 - root - ERROR - 获取邮件失败: 'TempMailAPI' object has no attribute 'get_mails'
2025-07-31 15:58:49,939 - __main__ - INFO - ==================================================
2025-07-31 15:58:49,939 - __main__ - INFO - TempMail 验证码获取工具启动
2025-07-31 15:58:49,940 - __main__ - INFO - ==================================================
2025-07-31 15:58:49,965 - __main__ - INFO - 创建主窗口...
2025-07-31 15:58:49,965 - usage_limiter - INFO - 已加载使用数据: 1 条记录
2025-07-31 15:58:50,692 - __main__ - INFO - 主窗口已显示
2025-07-31 15:58:50,692 - __main__ - INFO - 应用程序配置:
2025-07-31 15:58:50,692 - __main__ - INFO -   - 窗口大小: 520x420
2025-07-31 15:58:50,692 - __main__ - INFO -   - 每日使用限制: 50
2025-07-31 15:58:50,692 - __main__ - INFO -   - 请求超时: 10秒
2025-07-31 15:58:50,693 - __main__ - INFO -   - 日志级别: INFO
2025-07-31 15:58:50,693 - __main__ - INFO - 应用程序开始运行...
2025-07-31 15:59:02,174 - usage_limiter - INFO - 记录使用: <EMAIL> - get_quota, 今日已使用: 22/50
2025-07-31 15:59:02,179 - tempmail_api - INFO - 正在获取邮箱 <EMAIL> 的邮件...
2025-07-31 15:59:03,178 - tempmail_api - INFO - 成功获取 0 封邮件
2025-07-31 15:59:04,460 - usage_limiter - INFO - 记录使用: <EMAIL> - get_quota, 今日已使用: 23/50
2025-07-31 15:59:04,463 - tempmail_api - INFO - 正在获取邮箱 <EMAIL> 的邮件...
2025-07-31 15:59:05,473 - tempmail_api - INFO - 成功获取 0 封邮件
2025-07-31 16:01:07,638 - __main__ - INFO - ==================================================
2025-07-31 16:01:07,638 - __main__ - INFO - TempMail 验证码获取工具启动
2025-07-31 16:01:07,638 - __main__ - INFO - ==================================================
2025-07-31 16:01:07,664 - __main__ - INFO - 创建主窗口...
2025-07-31 16:01:07,665 - usage_limiter - INFO - 已加载使用数据: 1 条记录
2025-07-31 16:01:08,492 - __main__ - INFO - 主窗口已显示
2025-07-31 16:01:08,492 - __main__ - INFO - 应用程序配置:
2025-07-31 16:01:08,492 - __main__ - INFO -   - 窗口大小: 520x420
2025-07-31 16:01:08,492 - __main__ - INFO -   - 每日使用限制: 50
2025-07-31 16:01:08,493 - __main__ - INFO -   - 请求超时: 10秒
2025-07-31 16:01:08,493 - __main__ - INFO -   - 日志级别: INFO
2025-07-31 16:01:08,493 - __main__ - INFO - 应用程序开始运行...
2025-07-31 16:04:41,029 - __main__ - INFO - ==================================================
2025-07-31 16:04:41,029 - __main__ - INFO - TempMail 验证码获取工具启动
2025-07-31 16:04:41,029 - __main__ - INFO - ==================================================
2025-07-31 16:04:41,061 - __main__ - INFO - 创建主窗口...
2025-07-31 16:04:41,061 - usage_limiter - INFO - 已加载使用数据: 1 条记录
2025-07-31 16:04:41,959 - __main__ - INFO - 主窗口已显示
2025-07-31 16:04:41,959 - __main__ - INFO - 应用程序配置:
2025-07-31 16:04:41,960 - __main__ - INFO -   - 窗口大小: 520x420
2025-07-31 16:04:41,960 - __main__ - INFO -   - 每日使用限制: 50
2025-07-31 16:04:41,960 - __main__ - INFO -   - 请求超时: 10秒
2025-07-31 16:04:41,960 - __main__ - INFO -   - 日志级别: INFO
2025-07-31 16:04:41,961 - __main__ - INFO - 应用程序开始运行...
2025-07-31 16:04:55,997 - __main__ - INFO - 应用程序退出，退出码: 0
2025-07-31 16:05:13,177 - __main__ - INFO - ==================================================
2025-07-31 16:05:13,178 - __main__ - INFO - TempMail 验证码获取工具启动
2025-07-31 16:05:13,178 - __main__ - INFO - ==================================================
2025-07-31 16:05:13,213 - __main__ - INFO - 创建主窗口...
2025-07-31 16:05:13,213 - usage_limiter - INFO - 已加载使用数据: 1 条记录
2025-07-31 16:05:14,009 - __main__ - INFO - 主窗口已显示
2025-07-31 16:05:14,010 - __main__ - INFO - 应用程序配置:
2025-07-31 16:05:14,010 - __main__ - INFO -   - 窗口大小: 520x420
2025-07-31 16:05:14,010 - __main__ - INFO -   - 每日使用限制: 50
2025-07-31 16:05:14,010 - __main__ - INFO -   - 请求超时: 10秒
2025-07-31 16:05:14,011 - __main__ - INFO -   - 日志级别: INFO
2025-07-31 16:05:14,011 - __main__ - INFO - 应用程序开始运行...
2025-07-31 16:05:35,794 - __main__ - INFO - 应用程序退出，退出码: 0
2025-07-31 16:06:04,657 - __main__ - INFO - ==================================================
2025-07-31 16:06:04,658 - __main__ - INFO - TempMail 验证码获取工具启动
2025-07-31 16:06:04,658 - __main__ - INFO - ==================================================
2025-07-31 16:06:04,684 - __main__ - INFO - 创建主窗口...
2025-07-31 16:06:04,685 - usage_limiter - INFO - 已加载使用数据: 1 条记录
2025-07-31 16:06:05,335 - __main__ - INFO - 应用程序退出，退出码: 0
2025-07-31 16:06:05,383 - __main__ - INFO - 主窗口已显示
2025-07-31 16:06:05,383 - __main__ - INFO - 应用程序配置:
2025-07-31 16:06:05,383 - __main__ - INFO -   - 窗口大小: 520x420
2025-07-31 16:06:05,383 - __main__ - INFO -   - 每日使用限制: 50
2025-07-31 16:06:05,384 - __main__ - INFO -   - 请求超时: 10秒
2025-07-31 16:06:05,384 - __main__ - INFO -   - 日志级别: INFO
2025-07-31 16:06:05,384 - __main__ - INFO - 应用程序开始运行...
2025-07-31 16:06:15,417 - usage_limiter - INFO - 记录使用: <EMAIL> - get_quota, 今日已使用: 24/50
2025-07-31 16:06:15,420 - tempmail_api - INFO - 正在获取邮箱 <EMAIL> 的邮件...
2025-07-31 16:06:16,435 - tempmail_api - INFO - 成功获取 0 封邮件
2025-07-31 16:06:22,663 - __main__ - INFO - 应用程序退出，退出码: 0
2025-07-31 16:07:12,778 - __main__ - INFO - ==================================================
2025-07-31 16:07:12,778 - __main__ - INFO - TempMail 验证码获取工具启动
2025-07-31 16:07:12,778 - __main__ - INFO - ==================================================
2025-07-31 16:07:12,814 - __main__ - INFO - 创建主窗口...
2025-07-31 16:07:12,814 - usage_limiter - INFO - 已加载使用数据: 1 条记录
2025-07-31 16:07:13,698 - __main__ - INFO - 主窗口已显示
2025-07-31 16:07:13,699 - __main__ - INFO - 应用程序配置:
2025-07-31 16:07:13,699 - __main__ - INFO -   - 窗口大小: 520x420
2025-07-31 16:07:13,700 - __main__ - INFO -   - 每日使用限制: 50
2025-07-31 16:07:13,700 - __main__ - INFO -   - 请求超时: 10秒
2025-07-31 16:07:13,700 - __main__ - INFO -   - 日志级别: INFO
2025-07-31 16:07:13,700 - __main__ - INFO - 应用程序开始运行...
2025-07-31 16:07:21,381 - __main__ - INFO - 应用程序退出，退出码: 0
2025-07-31 16:09:07,929 - __main__ - INFO - ==================================================
2025-07-31 16:09:07,930 - __main__ - INFO - TempMail 验证码获取工具启动
2025-07-31 16:09:07,930 - __main__ - INFO - ==================================================
2025-07-31 16:09:07,960 - __main__ - INFO - 创建主窗口...
2025-07-31 16:09:07,960 - usage_limiter - INFO - 已加载使用数据: 1 条记录
2025-07-31 16:09:08,687 - __main__ - INFO - 主窗口已显示
2025-07-31 16:09:08,688 - __main__ - INFO - 应用程序配置:
2025-07-31 16:09:08,688 - __main__ - INFO -   - 窗口大小: 520x420
2025-07-31 16:09:08,688 - __main__ - INFO -   - 每日使用限制: 50
2025-07-31 16:09:08,688 - __main__ - INFO -   - 请求超时: 10秒
2025-07-31 16:09:08,688 - __main__ - INFO -   - 日志级别: INFO
2025-07-31 16:09:08,689 - __main__ - INFO - 应用程序开始运行...
2025-07-31 16:09:24,290 - __main__ - INFO - 应用程序退出，退出码: 0
2025-07-31 16:09:29,172 - __main__ - INFO - ==================================================
2025-07-31 16:09:29,173 - __main__ - INFO - TempMail 验证码获取工具启动
2025-07-31 16:09:29,173 - __main__ - INFO - ==================================================
2025-07-31 16:09:29,208 - __main__ - INFO - 创建主窗口...
2025-07-31 16:09:29,208 - usage_limiter - INFO - 已加载使用数据: 1 条记录
2025-07-31 16:09:29,990 - __main__ - INFO - 主窗口已显示
2025-07-31 16:09:29,991 - __main__ - INFO - 应用程序配置:
2025-07-31 16:09:29,991 - __main__ - INFO -   - 窗口大小: 520x420
2025-07-31 16:09:29,992 - __main__ - INFO -   - 每日使用限制: 50
2025-07-31 16:09:29,992 - __main__ - INFO -   - 请求超时: 10秒
2025-07-31 16:09:29,992 - __main__ - INFO -   - 日志级别: INFO
2025-07-31 16:09:29,992 - __main__ - INFO - 应用程序开始运行...
2025-07-31 16:09:33,977 - usage_limiter - INFO - 记录使用: <EMAIL> - get_quota, 今日已使用: 25/50
2025-07-31 16:09:33,981 - tempmail_api - INFO - 正在获取邮箱 <EMAIL> 的邮件...
2025-07-31 16:09:34,993 - tempmail_api - INFO - 成功获取 0 封邮件
2025-07-31 16:09:35,561 - usage_limiter - INFO - 记录使用: <EMAIL> - get_quota, 今日已使用: 26/50
2025-07-31 16:09:35,565 - tempmail_api - INFO - 正在获取邮箱 <EMAIL> 的邮件...
2025-07-31 16:09:36,572 - tempmail_api - INFO - 成功获取 0 封邮件
2025-07-31 16:09:36,938 - usage_limiter - INFO - 记录使用: <EMAIL> - get_quota, 今日已使用: 27/50
2025-07-31 16:09:36,943 - tempmail_api - INFO - 正在获取邮箱 <EMAIL> 的邮件...
2025-07-31 16:09:37,951 - tempmail_api - INFO - 成功获取 0 封邮件
2025-07-31 16:09:38,325 - usage_limiter - INFO - 记录使用: <EMAIL> - get_quota, 今日已使用: 28/50
2025-07-31 16:09:38,329 - tempmail_api - INFO - 正在获取邮箱 <EMAIL> 的邮件...
2025-07-31 16:09:39,325 - tempmail_api - INFO - 成功获取 0 封邮件
2025-07-31 16:09:39,682 - usage_limiter - INFO - 记录使用: <EMAIL> - get_quota, 今日已使用: 29/50
2025-07-31 16:09:39,686 - tempmail_api - INFO - 正在获取邮箱 <EMAIL> 的邮件...
2025-07-31 16:09:44,508 - tempmail_api - INFO - 成功获取 0 封邮件
2025-07-31 16:09:45,155 - usage_limiter - INFO - 记录使用: <EMAIL> - get_quota, 今日已使用: 30/50
2025-07-31 16:09:45,159 - tempmail_api - INFO - 正在获取邮箱 <EMAIL> 的邮件...
2025-07-31 16:09:46,158 - tempmail_api - INFO - 成功获取 0 封邮件
2025-07-31 16:09:47,470 - usage_limiter - INFO - 记录使用: <EMAIL> - get_quota, 今日已使用: 31/50
2025-07-31 16:09:47,474 - tempmail_api - INFO - 正在获取邮箱 <EMAIL> 的邮件...
2025-07-31 16:09:48,476 - tempmail_api - INFO - 成功获取 0 封邮件
2025-07-31 16:09:49,062 - usage_limiter - INFO - 记录使用: <EMAIL> - get_quota, 今日已使用: 32/50
2025-07-31 16:09:49,066 - tempmail_api - INFO - 正在获取邮箱 <EMAIL> 的邮件...
2025-07-31 16:09:51,089 - tempmail_api - INFO - 成功获取 0 封邮件
2025-07-31 16:09:51,918 - usage_limiter - INFO - 记录使用: <EMAIL> - get_quota, 今日已使用: 33/50
2025-07-31 16:09:51,922 - tempmail_api - INFO - 正在获取邮箱 <EMAIL> 的邮件...
2025-07-31 16:09:53,932 - tempmail_api - INFO - 成功获取 0 封邮件
2025-07-31 16:09:55,183 - usage_limiter - INFO - 记录使用: <EMAIL> - get_quota, 今日已使用: 34/50
2025-07-31 16:09:55,186 - tempmail_api - INFO - 正在获取邮箱 <EMAIL> 的邮件...
2025-07-31 16:09:57,318 - tempmail_api - INFO - 成功获取 0 封邮件
2025-07-31 16:09:58,186 - usage_limiter - INFO - 记录使用: <EMAIL> - get_quota, 今日已使用: 35/50
2025-07-31 16:09:58,190 - tempmail_api - INFO - 正在获取邮箱 <EMAIL> 的邮件...
2025-07-31 16:10:00,181 - tempmail_api - INFO - 成功获取 0 封邮件
2025-07-31 16:10:10,435 - __main__ - INFO - 应用程序退出，退出码: 0
2025-07-31 16:10:21,954 - __main__ - INFO - ==================================================
2025-07-31 16:10:21,954 - __main__ - INFO - TempMail 验证码获取工具启动
2025-07-31 16:10:21,954 - __main__ - INFO - ==================================================
2025-07-31 16:10:21,982 - __main__ - INFO - 创建主窗口...
2025-07-31 16:10:21,983 - usage_limiter - INFO - 已加载使用数据: 1 条记录
2025-07-31 16:10:22,866 - __main__ - INFO - 主窗口已显示
2025-07-31 16:10:22,867 - __main__ - INFO - 应用程序配置:
2025-07-31 16:10:22,867 - __main__ - INFO -   - 窗口大小: 520x420
2025-07-31 16:10:22,867 - __main__ - INFO -   - 每日使用限制: 50
2025-07-31 16:10:22,868 - __main__ - INFO -   - 请求超时: 10秒
2025-07-31 16:10:22,868 - __main__ - INFO -   - 日志级别: INFO
2025-07-31 16:10:22,868 - __main__ - INFO - 应用程序开始运行...
2025-07-31 16:10:28,243 - usage_limiter - INFO - 记录使用: <EMAIL> - get_quota, 今日已使用: 36/50
2025-07-31 16:10:28,246 - tempmail_api - INFO - 正在获取邮箱 <EMAIL> 的邮件...
2025-07-31 16:10:30,263 - tempmail_api - INFO - 成功获取 0 封邮件
2025-07-31 16:10:33,702 - __main__ - INFO - 应用程序退出，退出码: 0
2025-07-31 16:12:05,599 - __main__ - INFO - ==================================================
2025-07-31 16:12:05,599 - __main__ - INFO - TempMail 验证码获取工具启动
2025-07-31 16:12:05,599 - __main__ - INFO - ==================================================
2025-07-31 16:12:05,627 - __main__ - INFO - 创建主窗口...
2025-07-31 16:12:05,627 - usage_limiter - INFO - 已加载使用数据: 1 条记录
2025-07-31 16:12:06,596 - __main__ - INFO - 主窗口已显示
2025-07-31 16:12:06,597 - __main__ - INFO - 应用程序配置:
2025-07-31 16:12:06,597 - __main__ - INFO -   - 窗口大小: 520x420
2025-07-31 16:12:06,598 - __main__ - INFO -   - 每日使用限制: 50
2025-07-31 16:12:06,598 - __main__ - INFO -   - 请求超时: 10秒
2025-07-31 16:12:06,598 - __main__ - INFO -   - 日志级别: INFO
2025-07-31 16:12:06,598 - __main__ - INFO - 应用程序开始运行...
2025-07-31 16:12:14,124 - usage_limiter - INFO - 记录使用: <EMAIL> - get_quota, 今日已使用: 37/50
2025-07-31 16:12:14,127 - tempmail_api - INFO - 正在获取邮箱 <EMAIL> 的邮件...
2025-07-31 16:12:16,149 - tempmail_api - INFO - 成功获取 0 封邮件
2025-07-31 16:12:19,428 - __main__ - INFO - 应用程序退出，退出码: 0
2025-07-31 16:12:59,754 - __main__ - INFO - ==================================================
2025-07-31 16:12:59,754 - __main__ - INFO - TempMail 验证码获取工具启动
2025-07-31 16:12:59,754 - __main__ - INFO - ==================================================
2025-07-31 16:12:59,854 - __main__ - INFO - 创建主窗口...
2025-07-31 16:12:59,854 - usage_limiter - INFO - 已加载使用数据: 1 条记录
2025-07-31 16:13:00,621 - __main__ - INFO - 主窗口已显示
2025-07-31 16:13:00,621 - __main__ - INFO - 应用程序配置:
2025-07-31 16:13:00,621 - __main__ - INFO -   - 窗口大小: 520x420
2025-07-31 16:13:00,621 - __main__ - INFO -   - 每日使用限制: 50
2025-07-31 16:13:00,621 - __main__ - INFO -   - 请求超时: 10秒
2025-07-31 16:13:00,621 - __main__ - INFO -   - 日志级别: INFO
2025-07-31 16:13:00,622 - __main__ - INFO - 应用程序开始运行...
2025-07-31 16:14:01,904 - usage_limiter - INFO - 记录使用: <EMAIL> - get_quota, 今日已使用: 38/50
2025-07-31 16:14:01,908 - tempmail_api - INFO - 正在获取邮箱 <EMAIL> 的邮件...
2025-07-31 16:14:04,615 - tempmail_api - INFO - 成功获取 0 封邮件
2025-07-31 16:14:07,665 - usage_limiter - INFO - 记录使用: <EMAIL> - get_quota, 今日已使用: 39/50
2025-07-31 16:14:07,669 - tempmail_api - INFO - 正在获取邮箱 <EMAIL> 的邮件...
2025-07-31 16:14:10,702 - tempmail_api - INFO - 成功获取 0 封邮件
2025-07-31 16:14:21,005 - usage_limiter - INFO - 记录使用: <EMAIL> - get_quota, 今日已使用: 40/50
2025-07-31 16:14:21,010 - tempmail_api - INFO - 正在获取邮箱 <EMAIL> 的邮件...
2025-07-31 16:14:22,637 - tempmail_api - INFO - 成功获取 0 封邮件
2025-07-31 16:16:49,677 - __main__ - INFO - 应用程序退出，退出码: 0
2025-07-31 16:25:15,085 - __main__ - INFO - ==================================================
2025-07-31 16:25:15,085 - __main__ - INFO - TempMail 验证码获取工具启动
2025-07-31 16:25:15,086 - __main__ - INFO - ==================================================
2025-07-31 16:25:15,117 - __main__ - INFO - 创建主窗口...
2025-07-31 16:25:15,118 - usage_limiter - INFO - 已加载使用数据: 1 条记录
2025-07-31 16:25:16,094 - __main__ - INFO - 主窗口已显示
2025-07-31 16:25:16,094 - __main__ - INFO - 应用程序配置:
2025-07-31 16:25:16,095 - __main__ - INFO -   - 窗口大小: 520x420
2025-07-31 16:25:16,095 - __main__ - INFO -   - 每日使用限制: 50
2025-07-31 16:25:16,095 - __main__ - INFO -   - 请求超时: 10秒
2025-07-31 16:25:16,095 - __main__ - INFO -   - 日志级别: INFO
2025-07-31 16:25:16,095 - __main__ - INFO - 应用程序开始运行...
2025-07-31 16:25:26,821 - usage_limiter - INFO - 记录使用: <EMAIL> - get_quota, 今日已使用: 41/50
2025-07-31 16:25:26,824 - tempmail_api - INFO - 正在获取邮箱 <EMAIL> 的邮件...
2025-07-31 16:25:28,207 - tempmail_api - INFO - 成功获取 0 封邮件
2025-07-31 16:25:42,471 - __main__ - INFO - ==================================================
2025-07-31 16:25:42,471 - __main__ - INFO - TempMail 验证码获取工具启动
2025-07-31 16:25:42,471 - __main__ - INFO - ==================================================
2025-07-31 16:25:42,498 - __main__ - INFO - 创建主窗口...
2025-07-31 16:25:42,499 - usage_limiter - INFO - 已加载使用数据: 1 条记录
2025-07-31 16:25:43,370 - __main__ - INFO - 主窗口已显示
2025-07-31 16:25:43,370 - __main__ - INFO - 应用程序配置:
2025-07-31 16:25:43,371 - __main__ - INFO -   - 窗口大小: 520x420
2025-07-31 16:25:43,371 - __main__ - INFO -   - 每日使用限制: 50
2025-07-31 16:25:43,371 - __main__ - INFO -   - 请求超时: 10秒
2025-07-31 16:25:43,371 - __main__ - INFO -   - 日志级别: INFO
2025-07-31 16:25:43,371 - __main__ - INFO - 应用程序开始运行...
2025-07-31 16:25:51,936 - usage_limiter - INFO - 记录使用: <EMAIL> - get_quota, 今日已使用: 42/50
2025-07-31 16:25:51,940 - tempmail_api - INFO - 正在获取邮箱 <EMAIL> 的邮件...
2025-07-31 16:25:53,139 - tempmail_api - INFO - 成功获取 0 封邮件
2025-07-31 16:28:28,094 - __main__ - INFO - ==================================================
2025-07-31 16:28:28,094 - __main__ - INFO - TempMail 验证码获取工具启动
2025-07-31 16:28:28,094 - __main__ - INFO - ==================================================
2025-07-31 16:28:28,138 - __main__ - INFO - 创建主窗口...
2025-07-31 16:28:28,139 - usage_limiter - INFO - 已加载使用数据: 1 条记录
2025-07-31 16:28:29,185 - __main__ - INFO - 主窗口已显示
2025-07-31 16:28:29,186 - __main__ - INFO - 应用程序配置:
2025-07-31 16:28:29,186 - __main__ - INFO -   - 窗口大小: 520x420
2025-07-31 16:28:29,186 - __main__ - INFO -   - 每日使用限制: 50
2025-07-31 16:28:29,186 - __main__ - INFO -   - 请求超时: 10秒
2025-07-31 16:28:29,187 - __main__ - INFO -   - 日志级别: INFO
2025-07-31 16:28:29,187 - __main__ - INFO - 应用程序开始运行...
2025-07-31 16:29:11,444 - usage_limiter - INFO - 记录使用: <EMAIL> - get_quota, 今日已使用: 43/50
2025-07-31 16:29:11,447 - tempmail_api - INFO - 正在获取邮箱 <EMAIL> 的邮件...
2025-07-31 16:29:14,265 - tempmail_api - INFO - 成功获取 20 封邮件
2025-07-31 16:29:14,266 - __main__ - CRITICAL - 未捕获的异常
Traceback (most recent call last):
  File "E:\ai编程开发\augument-mail-plugin\main_window.py", line 182, in on_mails_fetched
    codes = self.extractor.extract_codes(content)
            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'CodeExtractor' object has no attribute 'extract_codes'
2025-07-31 16:31:04,194 - __main__ - INFO - 应用程序退出，退出码: 0
2025-07-31 16:31:09,886 - __main__ - INFO - 应用程序退出，退出码: 0
2025-07-31 16:34:36,215 - __main__ - INFO - ==================================================
2025-07-31 16:34:36,215 - __main__ - INFO - TempMail 验证码获取工具启动
2025-07-31 16:34:36,215 - __main__ - INFO - ==================================================
2025-07-31 16:34:36,244 - __main__ - INFO - 创建主窗口...
2025-07-31 16:34:36,244 - usage_limiter - INFO - 已加载使用数据: 1 条记录
2025-07-31 16:34:37,086 - __main__ - INFO - 主窗口已显示
2025-07-31 16:34:37,086 - __main__ - INFO - 应用程序配置:
2025-07-31 16:34:37,086 - __main__ - INFO -   - 窗口大小: 520x420
2025-07-31 16:34:37,086 - __main__ - INFO -   - 每日使用限制: 50
2025-07-31 16:34:37,086 - __main__ - INFO -   - 请求超时: 10秒
2025-07-31 16:34:37,087 - __main__ - INFO -   - 日志级别: INFO
2025-07-31 16:34:37,087 - __main__ - INFO - 应用程序开始运行...
2025-07-31 16:35:58,418 - usage_limiter - INFO - 记录使用: <EMAIL> - get_quota, 今日已使用: 44/50
2025-07-31 16:35:58,422 - tempmail_api - INFO - 正在获取邮箱 <EMAIL> 的邮件...
2025-07-31 16:35:59,429 - tempmail_api - INFO - 成功获取 20 封邮件
2025-07-31 16:41:49,723 - __main__ - INFO - ==================================================
2025-07-31 16:41:49,723 - __main__ - INFO - TempMail 验证码获取工具启动
2025-07-31 16:41:49,723 - __main__ - INFO - ==================================================
2025-07-31 16:41:49,750 - __main__ - INFO - 创建主窗口...
2025-07-31 16:41:49,750 - usage_limiter - INFO - 已加载使用数据: 1 条记录
2025-07-31 16:41:50,518 - __main__ - INFO - 主窗口已显示
2025-07-31 16:41:50,518 - __main__ - INFO - 应用程序配置:
2025-07-31 16:41:50,518 - __main__ - INFO -   - 窗口大小: 520x420
2025-07-31 16:41:50,519 - __main__ - INFO -   - 每日使用限制: 50
2025-07-31 16:41:50,519 - __main__ - INFO -   - 请求超时: 10秒
2025-07-31 16:41:50,519 - __main__ - INFO -   - 日志级别: INFO
2025-07-31 16:41:50,519 - __main__ - INFO - 应用程序开始运行...
2025-07-31 16:42:37,410 - usage_limiter - INFO - 记录使用: <EMAIL> - get_quota, 今日已使用: 45/50
2025-07-31 16:42:37,413 - tempmail_api - INFO - 正在获取邮箱 <EMAIL> 的邮件...
2025-07-31 16:42:40,349 - tempmail_api - INFO - 成功获取 20 封邮件
2025-07-31 16:42:40,350 - __main__ - CRITICAL - 未捕获的异常
Traceback (most recent call last):
  File "E:\ai编程开发\augument-mail-plugin\main_window.py", line 182, in on_mails_fetched
    self.status_updated.emit(f"📧 获取邮件 {i}/{len(mails)} 详情...")
    ^^^^^^^^^^^^^^^^^^^
AttributeError: 'MainWindow' object has no attribute 'status_updated'
2025-07-31 16:43:23,184 - usage_limiter - INFO - 记录使用: <EMAIL> - get_quota, 今日已使用: 46/50
2025-07-31 16:43:23,188 - tempmail_api - INFO - 正在获取邮箱 <EMAIL> 的邮件...
2025-07-31 16:43:24,182 - tempmail_api - INFO - 成功获取 20 封邮件
2025-07-31 16:43:24,183 - __main__ - CRITICAL - 未捕获的异常
Traceback (most recent call last):
  File "E:\ai编程开发\augument-mail-plugin\main_window.py", line 182, in on_mails_fetched
    self.status_updated.emit(f"📧 获取邮件 {i}/{len(mails)} 详情...")
    ^^^^^^^^^^^^^^^^^^^
AttributeError: 'MainWindow' object has no attribute 'status_updated'
2025-07-31 16:43:26,399 - __main__ - INFO - 应用程序退出，退出码: 0
2025-07-31 16:43:29,765 - usage_limiter - INFO - 记录使用: <EMAIL> - get_quota, 今日已使用: 45/50
2025-07-31 16:43:29,770 - tempmail_api - INFO - 正在获取邮箱 <EMAIL> 的邮件...
2025-07-31 16:43:30,762 - tempmail_api - INFO - 成功获取 20 封邮件
2025-07-31 16:43:32,875 - __main__ - INFO - 应用程序退出，退出码: 0
2025-07-31 16:43:38,460 - __main__ - INFO - ==================================================
2025-07-31 16:43:38,460 - __main__ - INFO - TempMail 验证码获取工具启动
2025-07-31 16:43:38,460 - __main__ - INFO - ==================================================
2025-07-31 16:43:38,488 - __main__ - INFO - 创建主窗口...
2025-07-31 16:43:38,489 - usage_limiter - INFO - 已加载使用数据: 1 条记录
2025-07-31 16:43:39,272 - __main__ - INFO - 主窗口已显示
2025-07-31 16:43:39,274 - __main__ - INFO - 应用程序配置:
2025-07-31 16:43:39,275 - __main__ - INFO -   - 窗口大小: 520x420
2025-07-31 16:43:39,276 - __main__ - INFO -   - 每日使用限制: 50
2025-07-31 16:43:39,276 - __main__ - INFO -   - 请求超时: 10秒
2025-07-31 16:43:39,277 - __main__ - INFO -   - 日志级别: INFO
2025-07-31 16:43:39,277 - __main__ - INFO - 应用程序开始运行...
2025-07-31 16:43:43,761 - usage_limiter - INFO - 记录使用: <EMAIL> - get_quota, 今日已使用: 46/50
2025-07-31 16:43:43,764 - tempmail_api - INFO - 正在获取邮箱 <EMAIL> 的邮件...
2025-07-31 16:43:45,540 - tempmail_api - INFO - 成功获取 20 封邮件
2025-07-31 16:43:45,540 - __main__ - CRITICAL - 未捕获的异常
Traceback (most recent call last):
  File "E:\ai编程开发\augument-mail-plugin\main_window.py", line 182, in on_mails_fetched
    self.status_updated.emit(f"📧 获取邮件 {i}/{len(mails)} 详情...")
    ^^^^^^^^^^^^^^^^^^^
AttributeError: 'MainWindow' object has no attribute 'status_updated'
2025-07-31 16:43:48,104 - __main__ - INFO - 应用程序退出，退出码: 0
2025-07-31 16:45:28,825 - __main__ - INFO - ==================================================
2025-07-31 16:45:28,825 - __main__ - INFO - TempMail 验证码获取工具启动
2025-07-31 16:45:28,826 - __main__ - INFO - ==================================================
2025-07-31 16:45:28,853 - __main__ - INFO - 创建主窗口...
2025-07-31 16:45:28,854 - usage_limiter - INFO - 已加载使用数据: 1 条记录
2025-07-31 16:45:29,668 - __main__ - INFO - 主窗口已显示
2025-07-31 16:45:29,668 - __main__ - INFO - 应用程序配置:
2025-07-31 16:45:29,668 - __main__ - INFO -   - 窗口大小: 520x420
2025-07-31 16:45:29,669 - __main__ - INFO -   - 每日使用限制: 50
2025-07-31 16:45:29,669 - __main__ - INFO -   - 请求超时: 10秒
2025-07-31 16:45:29,669 - __main__ - INFO -   - 日志级别: INFO
2025-07-31 16:45:29,669 - __main__ - INFO - 应用程序开始运行...
2025-07-31 16:45:49,389 - usage_limiter - INFO - 记录使用: <EMAIL> - get_quota, 今日已使用: 47/50
2025-07-31 16:45:49,392 - tempmail_api - INFO - 正在获取邮箱 <EMAIL> 的邮件...
2025-07-31 16:45:52,788 - tempmail_api - INFO - 成功获取 20 封邮件
2025-07-31 16:45:52,789 - __main__ - CRITICAL - 未捕获的异常
Traceback (most recent call last):
  File "E:\ai编程开发\augument-mail-plugin\main_window.py", line 186, in on_mails_fetched
    mail_detail = self.api.get_email_content(str(mail_id), self.email)
                  ^^^^^^^^
AttributeError: 'MainWindow' object has no attribute 'api'
2025-07-31 16:46:11,667 - usage_limiter - INFO - 记录使用: <EMAIL> - get_quota, 今日已使用: 48/50
2025-07-31 16:46:11,670 - tempmail_api - INFO - 正在获取邮箱 <EMAIL> 的邮件...
2025-07-31 16:46:14,486 - tempmail_api - INFO - 成功获取 20 封邮件
2025-07-31 16:46:14,486 - __main__ - CRITICAL - 未捕获的异常
Traceback (most recent call last):
  File "E:\ai编程开发\augument-mail-plugin\main_window.py", line 186, in on_mails_fetched
    mail_detail = self.api.get_email_content(str(mail_id), self.email)
                  ^^^^^^^^
AttributeError: 'MainWindow' object has no attribute 'api'
2025-07-31 16:46:19,197 - __main__ - INFO - 应用程序退出，退出码: 0
2025-07-31 16:50:06,145 - __main__ - INFO - ==================================================
2025-07-31 16:50:06,146 - __main__ - INFO - TempMail 验证码获取工具启动
2025-07-31 16:50:06,146 - __main__ - INFO - ==================================================
2025-07-31 16:50:06,175 - __main__ - INFO - 创建主窗口...
2025-07-31 16:50:06,176 - usage_limiter - INFO - 已加载使用数据: 1 条记录
2025-07-31 16:50:06,935 - __main__ - INFO - 主窗口已显示
2025-07-31 16:50:06,935 - __main__ - INFO - 应用程序配置:
2025-07-31 16:50:06,936 - __main__ - INFO -   - 窗口大小: 520x420
2025-07-31 16:50:06,936 - __main__ - INFO -   - 每日使用限制: 50
2025-07-31 16:50:06,936 - __main__ - INFO -   - 请求超时: 10秒
2025-07-31 16:50:06,936 - __main__ - INFO -   - 日志级别: INFO
2025-07-31 16:50:06,936 - __main__ - INFO - 应用程序开始运行...
2025-07-31 16:50:12,954 - usage_limiter - INFO - 记录使用: <EMAIL> - get_quota, 今日已使用: 49/50
2025-07-31 16:50:12,958 - tempmail_api - INFO - 正在获取邮箱 <EMAIL> 的邮件...
2025-07-31 16:50:13,968 - tempmail_api - INFO - 成功获取 20 封邮件
2025-07-31 16:50:13,969 - tempmail_api - INFO - 正在获取邮件 3702594142 的详细内容...
2025-07-31 16:50:14,961 - tempmail_api - INFO - 成功获取邮件详情，主题: Welcome to Augment Code
2025-07-31 16:50:14,965 - tempmail_api - INFO - 正在获取邮件 3702547133 的详细内容...
2025-07-31 16:50:15,502 - tempmail_api - INFO - 成功获取邮件详情，主题: Welcome to Augment Code
2025-07-31 16:50:15,504 - tempmail_api - INFO - 正在获取邮件 3702034114 的详细内容...
2025-07-31 16:50:16,050 - tempmail_api - INFO - 成功获取邮件详情，主题: Welcome to Augment Code
2025-07-31 16:50:16,052 - tempmail_api - INFO - 正在获取邮件 3694147142 的详细内容...
2025-07-31 16:50:17,067 - tempmail_api - INFO - 成功获取邮件详情，主题: Welcome to Augment Code
2025-07-31 16:50:17,069 - tempmail_api - INFO - 正在获取邮件 3694097992 的详细内容...
2025-07-31 16:50:17,607 - tempmail_api - INFO - 成功获取邮件详情，主题: Welcome to Augment Code
2025-07-31 16:50:17,608 - tempmail_api - INFO - 正在获取邮件 3694071081 的详细内容...
2025-07-31 16:50:17,882 - tempmail_api - INFO - 成功获取邮件详情，主题: Welcome to Augment Code
2025-07-31 16:50:17,885 - tempmail_api - INFO - 正在获取邮件 3693870021 的详细内容...
2025-07-31 16:50:18,156 - tempmail_api - INFO - 成功获取邮件详情，主题: Welcome to Augment Code
2025-07-31 16:50:18,157 - tempmail_api - INFO - 正在获取邮件 3693842831 的详细内容...
2025-07-31 16:50:18,430 - tempmail_api - INFO - 成功获取邮件详情，主题: Welcome to Augment Code
2025-07-31 16:50:18,432 - tempmail_api - INFO - 正在获取邮件 3693367091 的详细内容...
2025-07-31 16:50:18,703 - tempmail_api - INFO - 成功获取邮件详情，主题: Welcome to Augment Code
2025-07-31 16:50:18,706 - tempmail_api - INFO - 正在获取邮件 3693331115 的详细内容...
2025-07-31 16:50:22,112 - tempmail_api - INFO - 成功获取邮件详情，主题: Welcome to Augment Code
2025-07-31 16:50:22,115 - tempmail_api - INFO - 正在获取邮件 3692937281 的详细内容...
2025-07-31 16:50:22,973 - tempmail_api - INFO - 成功获取邮件详情，主题: Welcome to Augment Code
2025-07-31 16:50:22,976 - tempmail_api - INFO - 正在获取邮件 3692919238 的详细内容...
2025-07-31 16:50:24,081 - tempmail_api - INFO - 成功获取邮件详情，主题: Welcome to Augment Code
2025-07-31 16:50:24,083 - tempmail_api - INFO - 正在获取邮件 3692808426 的详细内容...
2025-07-31 16:50:24,624 - tempmail_api - INFO - 成功获取邮件详情，主题: Welcome to Augment Code
2025-07-31 16:50:24,626 - tempmail_api - INFO - 正在获取邮件 3692709811 的详细内容...
2025-07-31 16:50:24,895 - tempmail_api - INFO - 成功获取邮件详情，主题: Welcome to Augment Code
2025-07-31 16:50:24,897 - tempmail_api - INFO - 正在获取邮件 3692675279 的详细内容...
2025-07-31 16:50:25,165 - tempmail_api - INFO - 成功获取邮件详情，主题: Welcome to Augment Code
2025-07-31 16:50:25,168 - tempmail_api - INFO - 正在获取邮件 3692627804 的详细内容...
2025-07-31 16:50:26,195 - tempmail_api - INFO - 成功获取邮件详情，主题: Welcome to Augment Code
2025-07-31 16:50:26,196 - tempmail_api - INFO - 正在获取邮件 3692524394 的详细内容...
2025-07-31 16:50:26,733 - tempmail_api - INFO - 成功获取邮件详情，主题: Welcome to Augment Code
2025-07-31 16:50:26,735 - tempmail_api - INFO - 正在获取邮件 3692515898 的详细内容...
2025-07-31 16:50:30,108 - tempmail_api - INFO - 成功获取邮件详情，主题: Welcome to Augment Code
2025-07-31 16:50:30,112 - tempmail_api - INFO - 正在获取邮件 3690159163 的详细内容...
2025-07-31 16:50:30,652 - tempmail_api - INFO - 成功获取邮件详情，主题: Welcome to Augment Code
2025-07-31 16:50:30,656 - tempmail_api - INFO - 正在获取邮件 3690015128 的详细内容...
2025-07-31 16:50:31,195 - tempmail_api - INFO - 成功获取邮件详情，主题: Welcome to Augment Code
2025-07-31 16:53:40,850 - __main__ - INFO - ==================================================
2025-07-31 16:53:40,850 - __main__ - INFO - TempMail 验证码获取工具启动
2025-07-31 16:53:40,850 - __main__ - INFO - ==================================================
2025-07-31 16:53:40,924 - __main__ - INFO - 创建主窗口...
2025-07-31 16:53:40,925 - usage_limiter - INFO - 已加载使用数据: 1 条记录
2025-07-31 16:53:41,737 - __main__ - INFO - 主窗口已显示
2025-07-31 16:53:41,737 - __main__ - INFO - 应用程序配置:
2025-07-31 16:53:41,737 - __main__ - INFO -   - 窗口大小: 520x420
2025-07-31 16:53:41,737 - __main__ - INFO -   - 每日使用限制: 50
2025-07-31 16:53:41,737 - __main__ - INFO -   - 请求超时: 10秒
2025-07-31 16:53:41,738 - __main__ - INFO -   - 日志级别: INFO
2025-07-31 16:53:41,738 - __main__ - INFO - 应用程序开始运行...
2025-07-31 16:54:07,655 - usage_limiter - INFO - 记录使用: <EMAIL> - get_quota, 今日已使用: 50/50
2025-07-31 16:54:07,658 - tempmail_api - INFO - 正在获取邮箱 <EMAIL> 的邮件...
2025-07-31 16:54:10,453 - tempmail_api - INFO - 成功获取 20 封邮件
2025-07-31 16:54:10,454 - tempmail_api - INFO - 正在获取邮件 3702594142 的详细内容...
2025-07-31 16:54:12,457 - tempmail_api - INFO - 成功获取邮件详情，主题: Welcome to Augment Code
2025-07-31 16:54:27,452 - __main__ - INFO - 应用程序退出，退出码: 0
2025-07-31 17:01:36,602 - __main__ - INFO - ==================================================
2025-07-31 17:01:36,603 - __main__ - INFO - TempMail 验证码获取工具启动
2025-07-31 17:01:36,603 - __main__ - INFO - ==================================================
2025-07-31 17:01:36,628 - __main__ - INFO - 创建主窗口...
2025-07-31 17:01:36,629 - usage_limiter - INFO - 已加载使用数据: 1 条记录
2025-07-31 17:01:37,194 - auth_manager - INFO - 未找到授权数据
2025-07-31 17:01:37,195 - main_window - INFO - 未检测到有效授权，显示授权页面
2025-07-31 17:01:37,245 - __main__ - INFO - 主窗口已显示
2025-07-31 17:01:37,246 - __main__ - INFO - 应用程序配置:
2025-07-31 17:01:37,247 - __main__ - INFO -   - 窗口大小: 520x420
2025-07-31 17:01:37,247 - __main__ - INFO -   - 每日使用限制: 50
2025-07-31 17:01:37,248 - __main__ - INFO -   - 请求超时: 10秒
2025-07-31 17:01:37,248 - __main__ - INFO -   - 日志级别: INFO
2025-07-31 17:01:37,248 - __main__ - INFO - 应用程序开始运行...
2025-07-31 17:01:44,834 - auth_manager - INFO - 授权状态已保存
2025-07-31 17:01:44,841 - auth_manager - INFO - 授权状态保存成功
2025-07-31 17:01:44,842 - main_window - INFO - 授权状态已保存
2025-07-31 17:01:48,083 - __main__ - INFO - 应用程序退出，退出码: 0
2025-07-31 17:01:57,994 - __main__ - INFO - ==================================================
2025-07-31 17:01:57,994 - __main__ - INFO - TempMail 验证码获取工具启动
2025-07-31 17:01:57,995 - __main__ - INFO - ==================================================
2025-07-31 17:01:58,021 - __main__ - INFO - 创建主窗口...
2025-07-31 17:01:58,023 - usage_limiter - INFO - 已加载使用数据: 1 条记录
2025-07-31 17:01:58,508 - auth_manager - INFO - 授权状态已保存
2025-07-31 17:01:58,508 - auth_manager - INFO - 授权验证通过
2025-07-31 17:01:58,511 - main_window - INFO - 检测到有效授权，直接进入主页面
2025-07-31 17:01:58,822 - __main__ - INFO - 主窗口已显示
2025-07-31 17:01:58,822 - __main__ - INFO - 应用程序配置:
2025-07-31 17:01:58,822 - __main__ - INFO -   - 窗口大小: 520x420
2025-07-31 17:01:58,822 - __main__ - INFO -   - 每日使用限制: 50
2025-07-31 17:01:58,823 - __main__ - INFO -   - 请求超时: 10秒
2025-07-31 17:01:58,823 - __main__ - INFO -   - 日志级别: INFO
2025-07-31 17:01:58,823 - __main__ - INFO - 应用程序开始运行...
2025-07-31 17:02:12,399 - __main__ - INFO - 应用程序退出，退出码: 0
2025-07-31 17:02:23,750 - __main__ - INFO - ==================================================
2025-07-31 17:02:23,750 - __main__ - INFO - TempMail 验证码获取工具启动
2025-07-31 17:02:23,750 - __main__ - INFO - ==================================================
2025-07-31 17:02:23,803 - __main__ - INFO - 创建主窗口...
2025-07-31 17:02:23,804 - usage_limiter - INFO - 已加载使用数据: 1 条记录
2025-07-31 17:02:24,312 - auth_manager - INFO - 授权状态已保存
2025-07-31 17:02:24,312 - auth_manager - INFO - 授权验证通过
2025-07-31 17:02:24,315 - main_window - INFO - 检测到有效授权，直接进入主页面
2025-07-31 17:02:24,554 - __main__ - INFO - 主窗口已显示
2025-07-31 17:02:24,554 - __main__ - INFO - 应用程序配置:
2025-07-31 17:02:24,555 - __main__ - INFO -   - 窗口大小: 520x420
2025-07-31 17:02:24,556 - __main__ - INFO -   - 每日使用限制: 50
2025-07-31 17:02:24,556 - __main__ - INFO -   - 请求超时: 10秒
2025-07-31 17:02:24,556 - __main__ - INFO -   - 日志级别: INFO
2025-07-31 17:02:24,556 - __main__ - INFO - 应用程序开始运行...
2025-07-31 17:02:28,556 - __main__ - INFO - 应用程序退出，退出码: 0
2025-07-31 17:02:45,237 - __main__ - INFO - ==================================================
2025-07-31 17:02:45,238 - __main__ - INFO - TempMail 验证码获取工具启动
2025-07-31 17:02:45,238 - __main__ - INFO - ==================================================
2025-07-31 17:02:45,267 - __main__ - INFO - 创建主窗口...
2025-07-31 17:02:45,268 - usage_limiter - INFO - 已加载使用数据: 1 条记录
2025-07-31 17:02:45,788 - auth_manager - INFO - 授权状态已保存
2025-07-31 17:02:45,789 - auth_manager - INFO - 授权验证通过
2025-07-31 17:02:45,792 - main_window - INFO - 检测到有效授权，直接进入主页面
2025-07-31 17:02:46,040 - __main__ - INFO - 主窗口已显示
2025-07-31 17:02:46,041 - __main__ - INFO - 应用程序配置:
2025-07-31 17:02:46,041 - __main__ - INFO -   - 窗口大小: 520x420
2025-07-31 17:02:46,041 - __main__ - INFO -   - 每日使用限制: 50
2025-07-31 17:02:46,041 - __main__ - INFO -   - 请求超时: 10秒
2025-07-31 17:02:46,041 - __main__ - INFO -   - 日志级别: INFO
2025-07-31 17:02:46,042 - __main__ - INFO - 应用程序开始运行...
2025-07-31 17:02:52,165 - __main__ - INFO - 应用程序退出，退出码: 0
2025-07-31 17:07:47,907 - __main__ - INFO - ==================================================
2025-07-31 17:07:47,908 - __main__ - INFO - TempMail 验证码获取工具启动
2025-07-31 17:07:47,908 - __main__ - INFO - ==================================================
2025-07-31 17:07:47,935 - __main__ - INFO - 创建主窗口...
2025-07-31 17:07:48,420 - auth_manager - INFO - 授权状态已保存
2025-07-31 17:07:48,421 - auth_manager - INFO - 授权验证通过
2025-07-31 17:07:48,423 - main_window - INFO - 检测到有效授权，直接进入主页面
2025-07-31 17:07:48,678 - __main__ - INFO - 主窗口已显示
2025-07-31 17:07:48,678 - __main__ - INFO - 应用程序配置:
2025-07-31 17:07:48,678 - __main__ - INFO -   - 窗口大小: 520x420
2025-07-31 17:07:48,678 - __main__ - INFO -   - 每日使用限制: 50
2025-07-31 17:07:48,678 - __main__ - INFO -   - 请求超时: 10秒
2025-07-31 17:07:48,678 - __main__ - INFO -   - 日志级别: INFO
2025-07-31 17:07:48,679 - __main__ - INFO - 应用程序开始运行...
2025-07-31 17:26:01,047 - __main__ - INFO - 应用程序退出，退出码: 0
2025-08-01 11:17:40,666 - __main__ - INFO - ==================================================
2025-08-01 11:17:40,666 - __main__ - INFO - TempMail 验证码获取工具启动
2025-08-01 11:17:40,666 - __main__ - INFO - ==================================================
2025-08-01 11:17:40,730 - __main__ - INFO - 创建主窗口...
2025-08-01 11:17:41,228 - auth_manager - INFO - 授权状态已保存
2025-08-01 11:17:41,228 - auth_manager - INFO - 授权验证通过
2025-08-01 11:17:41,233 - main_window - INFO - 检测到有效授权，直接进入主页面
2025-08-01 11:17:41,510 - __main__ - INFO - 主窗口已显示
2025-08-01 11:17:41,510 - __main__ - INFO - 应用程序配置:
2025-08-01 11:17:41,511 - __main__ - INFO -   - 窗口大小: 520x420
2025-08-01 11:17:41,511 - __main__ - INFO -   - 每日使用限制: 50
2025-08-01 11:17:41,511 - __main__ - INFO -   - 请求超时: 10秒
2025-08-01 11:17:41,511 - __main__ - INFO -   - 日志级别: INFO
2025-08-01 11:17:41,511 - __main__ - INFO - 应用程序开始运行...
2025-08-01 11:17:43,056 - usage_limiter - INFO - 记录使用: <EMAIL> - get_quota, 今日已使用: 1/50
2025-08-01 11:17:43,059 - tempmail_api - INFO - 正在获取邮箱 <EMAIL> 的邮件...
2025-08-01 11:17:44,681 - tempmail_api - INFO - 成功获取 20 封邮件
2025-08-01 11:17:44,681 - tempmail_api - INFO - 正在获取邮件 3702594142 的详细内容...
2025-08-01 11:17:48,804 - tempmail_api - INFO - 成功获取邮件详情，主题: Welcome to Augment Code
2025-08-01 11:18:29,691 - __main__ - INFO - 应用程序退出，退出码: 0
2025-08-01 11:19:46,299 - __main__ - INFO - ==================================================
2025-08-01 11:19:46,300 - __main__ - INFO - TempMail 验证码获取工具启动
2025-08-01 11:19:46,300 - __main__ - INFO - ==================================================
2025-08-01 11:19:46,335 - __main__ - INFO - 创建主窗口...
2025-08-01 11:19:46,336 - usage_limiter - INFO - 已加载使用数据: 1 条记录
2025-08-01 11:19:46,992 - auth_manager - INFO - 授权状态已保存
2025-08-01 11:19:46,992 - auth_manager - INFO - 授权验证通过
2025-08-01 11:19:46,995 - main_window - INFO - 检测到有效授权，直接进入主页面
2025-08-01 11:19:47,264 - __main__ - INFO - 主窗口已显示
2025-08-01 11:19:47,265 - __main__ - INFO - 应用程序配置:
2025-08-01 11:19:47,265 - __main__ - INFO -   - 窗口大小: 520x420
2025-08-01 11:19:47,265 - __main__ - INFO -   - 每日使用限制: 50
2025-08-01 11:19:47,266 - __main__ - INFO -   - 请求超时: 10秒
2025-08-01 11:19:47,266 - __main__ - INFO -   - 日志级别: INFO
2025-08-01 11:19:47,267 - __main__ - INFO - 应用程序开始运行...
2025-08-01 11:20:10,392 - __main__ - INFO - ==================================================
2025-08-01 11:20:10,392 - __main__ - INFO - TempMail 验证码获取工具启动
2025-08-01 11:20:10,392 - __main__ - INFO - ==================================================
2025-08-01 11:20:10,437 - __main__ - INFO - 创建主窗口...
2025-08-01 11:20:10,437 - usage_limiter - INFO - 已加载使用数据: 1 条记录
2025-08-01 11:20:10,958 - auth_manager - INFO - 授权状态已保存
2025-08-01 11:20:10,958 - auth_manager - INFO - 授权验证通过
2025-08-01 11:20:10,961 - main_window - INFO - 检测到有效授权，直接进入主页面
2025-08-01 11:20:11,013 - __main__ - INFO - 主窗口已显示
2025-08-01 11:20:11,013 - __main__ - INFO - 应用程序配置:
2025-08-01 11:20:11,013 - __main__ - INFO -   - 窗口大小: 520x420
2025-08-01 11:20:11,014 - __main__ - INFO -   - 每日使用限制: 50
2025-08-01 11:20:11,014 - __main__ - INFO -   - 请求超时: 10秒
2025-08-01 11:20:11,014 - __main__ - INFO -   - 日志级别: INFO
2025-08-01 11:20:11,014 - __main__ - INFO - 应用程序开始运行...
2025-08-01 11:20:36,794 - __main__ - INFO - 应用程序退出，退出码: 0
2025-08-01 11:20:37,428 - __main__ - INFO - 应用程序退出，退出码: 0
2025-08-01 11:20:39,517 - __main__ - INFO - ==================================================
2025-08-01 11:20:39,517 - __main__ - INFO - TempMail 验证码获取工具启动
2025-08-01 11:20:39,517 - __main__ - INFO - ==================================================
2025-08-01 11:20:39,554 - __main__ - INFO - 创建主窗口...
2025-08-01 11:20:39,555 - usage_limiter - INFO - 已加载使用数据: 1 条记录
2025-08-01 11:20:40,313 - auth_manager - INFO - 授权状态已保存
2025-08-01 11:20:40,313 - auth_manager - INFO - 授权验证通过
2025-08-01 11:20:40,317 - main_window - INFO - 检测到有效授权，直接进入主页面
2025-08-01 11:20:40,589 - __main__ - INFO - 主窗口已显示
2025-08-01 11:20:40,590 - __main__ - INFO - 应用程序配置:
2025-08-01 11:20:40,590 - __main__ - INFO -   - 窗口大小: 520x420
2025-08-01 11:20:40,590 - __main__ - INFO -   - 每日使用限制: 50
2025-08-01 11:20:40,590 - __main__ - INFO -   - 请求超时: 10秒
2025-08-01 11:20:40,590 - __main__ - INFO -   - 日志级别: INFO
2025-08-01 11:20:40,590 - __main__ - INFO - 应用程序开始运行...
2025-08-01 11:20:46,930 - __main__ - INFO - 应用程序退出，退出码: 0
2025-08-01 11:21:03,947 - __main__ - INFO - ==================================================
2025-08-01 11:21:03,948 - __main__ - INFO - TempMail 验证码获取工具启动
2025-08-01 11:21:03,948 - __main__ - INFO - ==================================================
2025-08-01 11:21:03,982 - __main__ - INFO - 创建主窗口...
2025-08-01 11:21:03,983 - usage_limiter - INFO - 已加载使用数据: 1 条记录
2025-08-01 11:21:04,524 - auth_manager - INFO - 授权状态已保存
2025-08-01 11:21:04,524 - auth_manager - INFO - 授权验证通过
2025-08-01 11:21:04,527 - main_window - INFO - 检测到有效授权，直接进入主页面
2025-08-01 11:21:04,803 - __main__ - INFO - 主窗口已显示
2025-08-01 11:21:04,804 - __main__ - INFO - 应用程序配置:
2025-08-01 11:21:04,804 - __main__ - INFO -   - 窗口大小: 520x480
2025-08-01 11:21:04,804 - __main__ - INFO -   - 每日使用限制: 50
2025-08-01 11:21:04,805 - __main__ - INFO -   - 请求超时: 10秒
2025-08-01 11:21:04,806 - __main__ - INFO -   - 日志级别: INFO
2025-08-01 11:21:04,806 - __main__ - INFO - 应用程序开始运行...
2025-08-01 11:21:31,753 - __main__ - INFO - ==================================================
2025-08-01 11:21:31,754 - __main__ - INFO - TempMail 验证码获取工具启动
2025-08-01 11:21:31,754 - __main__ - INFO - ==================================================
2025-08-01 11:21:31,784 - __main__ - INFO - 创建主窗口...
2025-08-01 11:21:31,785 - usage_limiter - INFO - 已加载使用数据: 1 条记录
2025-08-01 11:21:32,503 - auth_manager - INFO - 授权状态已保存
2025-08-01 11:21:32,504 - auth_manager - INFO - 授权验证通过
2025-08-01 11:21:32,506 - main_window - INFO - 检测到有效授权，直接进入主页面
2025-08-01 11:21:32,780 - __main__ - INFO - 主窗口已显示
2025-08-01 11:21:32,780 - __main__ - INFO - 应用程序配置:
2025-08-01 11:21:32,781 - __main__ - INFO -   - 窗口大小: 520x790
2025-08-01 11:21:32,781 - __main__ - INFO -   - 每日使用限制: 50
2025-08-01 11:21:32,781 - __main__ - INFO -   - 请求超时: 10秒
2025-08-01 11:21:32,781 - __main__ - INFO -   - 日志级别: INFO
2025-08-01 11:21:32,781 - __main__ - INFO - 应用程序开始运行...
2025-08-01 11:21:33,905 - __main__ - INFO - 应用程序退出，退出码: 0
2025-08-01 11:21:39,827 - __main__ - INFO - ==================================================
2025-08-01 11:21:39,827 - __main__ - INFO - TempMail 验证码获取工具启动
2025-08-01 11:21:39,827 - __main__ - INFO - ==================================================
2025-08-01 11:21:39,858 - __main__ - INFO - 创建主窗口...
2025-08-01 11:21:39,858 - usage_limiter - INFO - 已加载使用数据: 1 条记录
2025-08-01 11:21:40,389 - auth_manager - INFO - 授权状态已保存
2025-08-01 11:21:40,389 - auth_manager - INFO - 授权验证通过
2025-08-01 11:21:40,393 - main_window - INFO - 检测到有效授权，直接进入主页面
2025-08-01 11:21:40,670 - __main__ - INFO - 主窗口已显示
2025-08-01 11:21:40,670 - __main__ - INFO - 应用程序配置:
2025-08-01 11:21:40,670 - __main__ - INFO -   - 窗口大小: 520x490
2025-08-01 11:21:40,671 - __main__ - INFO -   - 每日使用限制: 50
2025-08-01 11:21:40,671 - __main__ - INFO -   - 请求超时: 10秒
2025-08-01 11:21:40,671 - __main__ - INFO -   - 日志级别: INFO
2025-08-01 11:21:40,671 - __main__ - INFO - 应用程序开始运行...
2025-08-01 11:21:42,786 - __main__ - INFO - 应用程序退出，退出码: 0
2025-08-01 11:22:02,314 - __main__ - INFO - ==================================================
2025-08-01 11:22:02,315 - __main__ - INFO - TempMail 验证码获取工具启动
2025-08-01 11:22:02,315 - __main__ - INFO - ==================================================
2025-08-01 11:22:02,348 - __main__ - INFO - 创建主窗口...
2025-08-01 11:22:02,349 - usage_limiter - INFO - 已加载使用数据: 1 条记录
2025-08-01 11:22:02,962 - auth_manager - INFO - 授权状态已保存
2025-08-01 11:22:02,962 - auth_manager - INFO - 授权验证通过
2025-08-01 11:22:02,966 - main_window - INFO - 检测到有效授权，直接进入主页面
2025-08-01 11:22:03,261 - __main__ - INFO - 主窗口已显示
2025-08-01 11:22:03,262 - __main__ - INFO - 应用程序配置:
2025-08-01 11:22:03,262 - __main__ - INFO -   - 窗口大小: 520x480
2025-08-01 11:22:03,263 - __main__ - INFO -   - 每日使用限制: 50
2025-08-01 11:22:03,263 - __main__ - INFO -   - 请求超时: 10秒
2025-08-01 11:22:03,264 - __main__ - INFO -   - 日志级别: INFO
2025-08-01 11:22:03,264 - __main__ - INFO - 应用程序开始运行...
2025-08-01 11:22:04,933 - usage_limiter - INFO - 记录使用: <EMAIL> - get_quota, 今日已使用: 2/50
2025-08-01 11:22:04,936 - tempmail_api - INFO - 正在获取邮箱 <EMAIL> 的邮件...
2025-08-01 11:22:09,097 - tempmail_api - INFO - 成功获取 20 封邮件
2025-08-01 11:22:09,097 - tempmail_api - INFO - 正在获取邮件 3702594142 的详细内容...
2025-08-01 11:22:13,606 - tempmail_api - INFO - 成功获取邮件详情，主题: Welcome to Augment Code
2025-08-01 11:22:28,264 - __main__ - INFO - 应用程序退出，退出码: 0
