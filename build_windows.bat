@echo off
echo ========================================
echo TempMail Tool - Windows Build Script
echo ========================================
echo.

:: Set variables
set APP_NAME=TempMail验证码获取工具
set BUILD_DIR=dist
set SPEC_FILE=tempmail_tool.spec

:: Check Python environment
echo [1/6] Checking Python environment...
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python not found, please ensure Python is installed and added to PATH
    pause
    exit /b 1
)
echo Python environment check passed

:: Check and install PyInstaller
echo.
echo [2/6] Checking PyInstaller...
python -c "import PyInstaller" >nul 2>&1
if errorlevel 1 (
    echo Installing PyInstaller...
    pip install pyinstaller
    if errorlevel 1 (
        echo Error: PyInstaller installation failed
        pause
        exit /b 1
    )
) else (
    echo PyInstaller is already installed
)

:: Install project dependencies
echo.
echo [3/6] Installing project dependencies...
if exist requirements.txt (
    pip install -r requirements.txt
    if errorlevel 1 (
        echo Warning: Some dependencies failed to install, continuing with build...
    )
) else (
    echo Warning: requirements.txt file not found
)

:: 清理之前的构建
echo.
echo [4/6] 清理之前的构建...
if exist %BUILD_DIR% (
    echo 删除旧的构建目录...
    rmdir /s /q %BUILD_DIR%
)
if exist build (
    echo 删除临时构建目录...
    rmdir /s /q build
)

:: 创建图标文件（如果不存在）
echo.
echo [5/6] 准备资源文件...
if not exist icon.ico (
    if exist icon.png (
        echo 注意: 建议将icon.png转换为icon.ico以获得更好的Windows兼容性
    ) else (
        echo 警告: 未找到应用程序图标文件
    )
)

:: 执行打包
echo.
echo [6/6] 开始打包应用程序...
echo 这可能需要几分钟时间，请耐心等待...
echo.

pyinstaller --clean %SPEC_FILE%

:: 检查打包结果
if errorlevel 1 (
    echo.
    echo ========================================
    echo 打包失败！
    echo ========================================
    echo 请检查上面的错误信息并修复问题后重试
    pause
    exit /b 1
)

:: 检查输出文件
if exist "%BUILD_DIR%\%APP_NAME%.exe" (
    echo.
    echo ========================================
    echo 打包成功！
    echo ========================================
    echo 应用程序位置: %BUILD_DIR%\%APP_NAME%.exe
    echo 文件大小:
    dir "%BUILD_DIR%\%APP_NAME%.exe" | findstr "%APP_NAME%.exe"
    echo.
    echo 提示:
    echo 1. 可以直接运行 %BUILD_DIR%\%APP_NAME%.exe
    echo 2. 该文件包含所有依赖，可以在其他Windows电脑上独立运行
    echo 3. 首次运行可能需要几秒钟启动时间
    echo.
    
    :: 询问是否立即测试
    set /p test_choice="是否立即测试应用程序？(y/n): "
    if /i "%test_choice%"=="y" (
        echo 启动应用程序进行测试...
        start "" "%BUILD_DIR%\%APP_NAME%.exe"
    )
    
) else (
    echo.
    echo ========================================
    echo 打包可能失败！
    echo ========================================
    echo 未在 %BUILD_DIR% 目录中找到可执行文件
    echo 请检查上面的输出信息
)

echo.
echo 打包脚本执行完成
pause
