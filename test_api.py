#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试tempmail.plus API调用
"""

import requests
import json
from urllib.parse import quote

def test_tempmail_api():
    """测试tempmail.plus API"""
    
    # 测试参数
    email = "<EMAIL>"
    epin = "123456"
    
    # 构建URL
    base_url = "https://tempmail.plus"
    api_url = f"{base_url}/api/mails"
    
    # 查询参数
    params = {
        'email': email,
        'epin': epin,
        'first_id': 0
    }
    
    # 请求头
    headers = {
        'accept': 'application/json, text/javascript, */*; q=0.01',
        'accept-language': 'zh,zh-CN;q=0.9',
        'sec-ch-ua': '"Google Chrome";v="125", "Chromium";v="125", "Not.A/Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'x-requested-with': 'XMLHttpRequest',
        'referer': f'{base_url}/zh/'
    }
    
    try:
        print(f"正在测试API调用...")
        print(f"URL: {api_url}")
        print(f"参数: {params}")
        
        response = requests.get(
            api_url,
            params=params,
            headers=headers,
            timeout=10
        )
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"响应数据: {json.dumps(data, indent=2, ensure_ascii=False)}")
            
            # 检查数据结构
            if 'mail_list' in data:
                mail_count = len(data['mail_list'])
                print(f"✅ 成功获取 {mail_count} 封邮件")
                
                if mail_count > 0:
                    latest_mail = data['mail_list'][0]
                    print(f"最新邮件信息:")
                    print(f"  - ID: {latest_mail.get('mail_id')}")
                    print(f"  - 发件人: {latest_mail.get('from_mail')}")
                    print(f"  - 主题: {latest_mail.get('subject')}")
                    print(f"  - 时间: {latest_mail.get('time')}")
                else:
                    print("📭 当前邮箱没有邮件")
            else:
                print("❌ 响应格式不正确，缺少mail_list字段")
        else:
            print(f"❌ 请求失败，状态码: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")

if __name__ == "__main__":
    test_tempmail_api()
