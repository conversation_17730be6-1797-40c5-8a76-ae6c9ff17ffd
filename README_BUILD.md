# TempMail验证码获取工具 - 打包说明

本文档说明如何将TempMail验证码获取工具打包成独立的应用程序文件。

## 支持的平台

- **Windows**: 生成 `.exe` 可执行文件
- **macOS**: 生成 `.app` 应用程序包，可选择创建 `.dmg` 安装包

## 快速开始

### Windows 平台

**简单方式（推荐）：**
1. 确保已安装 Python 3.8+
2. 双击运行 `build_simple.bat`
3. 等待打包完成，生成的应用程序位于 `dist/TempMailTool.exe`

**完整方式：**
1. 双击运行 `build_windows.bat`（如果遇到编码问题，使用简单方式）

### macOS 平台

**简单方式（推荐）：**
1. 确保已安装 Python 3.8+
2. 在终端中运行：
   ```bash
   chmod +x build_simple_macos.sh
   ./build_simple_macos.sh
   ```
3. 等待打包完成，生成的应用程序位于 `dist/TempMailTool.app`

**完整方式：**
1. 在终端中运行：
   ```bash
   chmod +x build_macos.sh
   ./build_macos.sh
   ```

## 手动打包步骤

如果自动脚本遇到问题，可以手动执行以下步骤：

### 1. 安装依赖

```bash
# 安装运行时依赖
pip install -r requirements.txt

# 安装打包工具
pip install pyinstaller
```

### 2. 执行打包

```bash
# 使用配置文件打包
pyinstaller --clean tempmail_tool.spec
```

### 3. 测试应用程序

打包完成后，在 `dist/` 目录中找到生成的应用程序并测试运行。

## 打包配置说明

### 主要配置文件

- `tempmail_tool.spec`: PyInstaller 配置文件，包含所有打包设置
- `build_requirements.txt`: 打包专用依赖文件
- `hooks/`: PyInstaller 钩子文件，确保特定模块正确打包

### 优化设置

1. **体积优化**:
   - 排除不必要的模块（如 matplotlib, numpy 等）
   - 启用 UPX 压缩（如果可用）
   - 只包含必要的 PySide6 模块

2. **兼容性优化**:
   - 包含所有必要的隐藏导入
   - 正确处理资源文件（图标等）
   - 支持高DPI显示

3. **启动优化**:
   - 禁用控制台窗口（Windows）
   - 设置正确的应用程序元数据

## 常见问题

### 1. 打包失败

**问题**: PyInstaller 报错找不到模块
**解决**: 检查 `tempmail_tool.spec` 中的 `hiddenimports` 列表，添加缺失的模块

**问题**: 缺少依赖
**解决**: 运行 `pip install -r build_requirements.txt` 安装所有依赖

### 2. 应用程序无法启动

**问题**: 双击应用程序没有反应
**解决**: 
- Windows: 在命令行中运行应用程序查看错误信息
- macOS: 在终端中运行 `./应用程序.app/Contents/MacOS/应用程序名` 查看错误

**问题**: 缺少资源文件
**解决**: 确保 `icon.png` 等资源文件存在于项目根目录

### 3. macOS 安全警告

**问题**: macOS 提示应用程序来自未知开发者
**解决**: 
1. 右键点击应用程序，选择"打开"
2. 或在系统偏好设置 > 安全性与隐私中允许运行

### 4. 应用程序体积过大

**解决方案**:
1. 检查是否安装了不必要的 Python 包
2. 在 `tempmail_tool.spec` 的 `excludes` 列表中添加更多不需要的模块
3. 确保 UPX 压缩工具可用

## 文件结构

打包后的文件结构：

```
dist/
├── TempMail验证码获取工具.exe          # Windows 可执行文件
└── TempMail验证码获取工具.app/         # macOS 应用程序包
    └── Contents/
        ├── MacOS/
        ├── Resources/
        └── Info.plist
```

## 分发说明

### Windows

- 生成的 `.exe` 文件可以直接在其他 Windows 电脑上运行
- 不需要安装 Python 或其他依赖
- 建议创建安装程序或压缩包分发

### macOS

- 生成的 `.app` 文件可以直接在其他 macOS 电脑上运行
- 可以创建 `.dmg` 安装包便于分发
- 首次运行可能需要在安全设置中允许

## 技术细节

- **打包工具**: PyInstaller 5.13+
- **GUI框架**: PySide6
- **Python版本**: 3.8+
- **打包模式**: 单文件模式（Windows）/ 应用程序包模式（macOS）
- **启动时间**: 首次启动约 3-5 秒（后续启动更快）
