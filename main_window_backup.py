"""
主GUI界面
使用PySide6实现的邮件验证码获取工具界面
"""

import sys
import logging
import random
import string
from datetime import datetime
from typing import List, Dict
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QLineEdit, QPushButton, QTextEdit, QProgressBar,
    QGroupBox, QSplitter, QStatusBar, QMessageBox, QFrame, QStackedWidget
)
from PySide6.QtCore import Qt, QThread, QTimer, Signal, QSize
from PySide6.QtGui import QFont, QIcon, QPixmap

import config
from tempmail_api import TempMailAPI
from code_extractor import CodeExtractor
from usage_limiter import UsageLimiter

# 设置日志
logging.basicConfig(level=getattr(logging, config.LOG_LEVEL), format=config.LOG_FORMAT)
logger = logging.getLogger(__name__)


class EmailFetchWorker(QThread):
    """邮件获取工作线程"""

    # 信号定义
    progress_updated = Signal(str)  # 进度更新
    emails_received = Signal(dict)  # 邮件接收完成
    error_occurred = Signal(str)    # 错误发生

    def __init__(self, email: str):
        super().__init__()
        self.email = email
        self.api = TempMailAPI()

    def run(self):
        """执行邮件获取"""
        try:
            self.progress_updated.emit("正在连接服务器...")

            # 验证邮箱格式
            if not self.api.validate_email_format(self.email):
                self.error_occurred.emit("邮箱格式不正确或不支持该域名")
                return

            self.progress_updated.emit("正在获取邮件列表...")

            # 获取邮件（使用默认的epin）
            result = self.api.get_emails(self.email, epin=config.DEFAULT_EPIN)

            if "error" in result:
                self.error_occurred.emit(result["error"])
                return

            self.progress_updated.emit("邮件获取完成")
            self.emails_received.emit(result)

        except Exception as e:
            logger.error(f"邮件获取失败: {e}")
            self.error_occurred.emit(f"获取邮件时发生错误: {str(e)}")
        finally:
            self.api.close()


class MainWindow(QMainWindow):
    """主窗口"""
    
    def __init__(self):
        super().__init__()
        self.usage_limiter = UsageLimiter()
        self.code_extractor = CodeExtractor()
        self.worker = None
        
        self.init_ui()
    
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle(config.WINDOW_TITLE)
        self.setMinimumSize(config.WINDOW_MIN_WIDTH, config.WINDOW_MIN_HEIGHT)
        self.resize(config.WINDOW_WIDTH, config.WINDOW_HEIGHT)

        # 创建堆叠控件作为中央控件
        self.stacked_widget = QStackedWidget()
        self.setCentralWidget(self.stacked_widget)

        # 创建授权页面
        self.create_auth_page()

        # 创建主功能页面
        self.create_main_page()

        # 创建状态栏
        self.create_status_bar()

        # 设置样式
        self.setStyleSheet(self.get_stylesheet())

        # 默认显示授权页面
        self.stacked_widget.setCurrentIndex(0)

    def generate_random_email(self):
        """生成随机邮箱地址"""
        # 生成随机用户名（6-10位字母数字组合）
        username_length = random.randint(6, 10)
        username = ''.join(random.choices(string.ascii_lowercase + string.digits, k=username_length))
        return f"{username}@aicgbot.com"

    def generate_new_random_email(self):
        """生成新的随机邮箱并更新输入框"""
        new_email = self.generate_random_email()
        self.email_input.setText(new_email)

    def create_auth_page(self):
        """创建授权页面"""
        auth_widget = QWidget()
        auth_layout = QVBoxLayout(auth_widget)
        auth_layout.setSpacing(15)
        auth_layout.setContentsMargins(30, 40, 30, 40)

        # 标题
        title_label = QLabel("🔐 Augment Code")
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                font-size: 20px;
                font-weight: bold;
                color: #4A90E2;
                margin-bottom: 5px;
                padding: 5px;
            }
        """)

        # 副标题
        subtitle_label = QLabel("激活额度获取工具")
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setStyleSheet("""
            QLabel {
                font-size: 14px;
                color: #AAA;
                margin-bottom: 20px;
                padding: 3px;
            }
        """)

        # 授权说明
        info_label = QLabel("🛡️ 请输入授权码以继续使用")
        info_label.setAlignment(Qt.AlignCenter)
        info_label.setStyleSheet("""
            QLabel {
                font-size: 13px;
                color: #E0E0E0;
                margin-bottom: 15px;
                padding: 3px;
            }
        """)

        # 授权码输入框
        self.auth_input = QLineEdit()
        self.auth_input.setPlaceholderText("请输入6位授权码")
        self.auth_input.setMaxLength(6)
        self.auth_input.setAlignment(Qt.AlignCenter)
        self.auth_input.setStyleSheet("""
            QLineEdit {
                background-color: #3A3A3A;
                border: 2px solid #555;
                border-radius: 6px;
                padding: 10px 14px;
                color: #E0E0E0;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 14px;
                font-weight: bold;
                letter-spacing: 1px;
            }
            QLineEdit:focus {
                border-color: #4A90E2;
            }
        """)
        self.auth_input.returnPressed.connect(self.verify_auth_code)

        # 验证按钮
        self.auth_button = QPushButton("🚀 验证授权")
        self.auth_button.clicked.connect(self.verify_auth_code)
        self.auth_button.setStyleSheet("""
            QPushButton {
                background-color: #4A90E2;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #357ABD;
            }
            QPushButton:pressed {
                background-color: #2E6DA4;
            }
        """)

        # 错误提示标签
        self.auth_error_label = QLabel("")
        self.auth_error_label.setAlignment(Qt.AlignCenter)
        self.auth_error_label.setStyleSheet("""
            QLabel {
                color: #F44336;
                font-size: 11px;
                margin-top: 8px;
                padding: 2px;
            }
        """)
        self.auth_error_label.hide()

        # 添加到布局
        auth_layout.addStretch()
        auth_layout.addWidget(title_label)
        auth_layout.addWidget(subtitle_label)
        auth_layout.addWidget(info_label)
        auth_layout.addWidget(self.auth_input)
        auth_layout.addWidget(self.auth_button)
        auth_layout.addWidget(self.auth_error_label)
        auth_layout.addStretch()

        # 添加到堆叠控件
        self.stacked_widget.addWidget(auth_widget)

    def create_main_page(self):
        """创建主功能页面"""
        main_widget = QWidget()
        main_layout = QVBoxLayout(main_widget)
        main_layout.setSpacing(8)
        main_layout.setContentsMargins(12, 12, 12, 12)

        # 创建各个组件
        self.create_input_section(main_layout)
        self.create_progress_section(main_layout)
        self.create_content_section(main_layout)

        # 添加到堆叠控件
        self.stacked_widget.addWidget(main_widget)

    def verify_auth_code(self):
        """验证授权码"""
        entered_code = self.auth_input.text().strip()

        if entered_code == config.AUTH_CODE:
            # 授权成功，切换到主页面
            self.stacked_widget.setCurrentIndex(1)
            self.status_bar.showMessage("🟢 就绪")
            self.update_usage_display()

            # 清空授权输入框
            self.auth_input.clear()
            self.auth_error_label.hide()
        else:
            # 授权失败，显示错误信息
            self.auth_error_label.setText("❌ 授权码错误，请重试")
            self.auth_error_label.show()
            self.auth_input.clear()
            self.auth_input.setFocus()

    def create_input_section(self, parent_layout):
        """创建输入区域"""
        input_group = QGroupBox("🔧 Augment Code 激活额度")
        input_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 13px;
                color: #4A90E2;
                border: 2px solid #4A90E2;
                border-radius: 8px;
                margin-top: 8px;
                padding-top: 8px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 12px;
                padding: 0 8px 0 8px;
                background-color: #2B2B2B;
            }
        """)
        input_layout = QVBoxLayout(input_group)
        input_layout.setSpacing(6)

        # 邮箱输入行
        email_layout = QHBoxLayout()
        email_layout.setSpacing(6)

        email_label = QLabel("邮箱:")
        email_label.setMinimumWidth(50)
        email_label.setStyleSheet("color: #E0E0E0; font-weight: bold;")

        self.email_input = QLineEdit()
        self.email_input.setPlaceholderText("激活额度邮箱地址")
        self.email_input.setText(self.generate_random_email())
        self.email_input.setReadOnly(True)
        self.email_input.setStyleSheet("""
            QLineEdit {
                background-color: #3A3A3A;
                border: 1px solid #555;
                border-radius: 4px;
                padding: 6px 8px;
                color: #E0E0E0;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
            }
        """)

        # 随机生成按钮
        self.random_button = QPushButton("🎲")
        self.random_button.setMaximumWidth(32)
        self.random_button.setMaximumHeight(32)
        self.random_button.setToolTip("生成新的随机邮箱")
        self.random_button.clicked.connect(self.generate_new_random_email)
        self.random_button.setStyleSheet("""
            QPushButton {
                background-color: #5A5A5A;
                border: 1px solid #777;
                border-radius: 4px;
                font-size: 14px;
            }
            QPushButton:hover {
                background-color: #6A6A6A;
            }
            QPushButton:pressed {
                background-color: #4A4A4A;
            }
        """)

        self.fetch_button = QPushButton("🚀 获取激活额度")
        self.fetch_button.setMinimumWidth(120)
        self.fetch_button.clicked.connect(self.fetch_quota)
        self.fetch_button.setStyleSheet("""
            QPushButton {
                background-color: #4A90E2;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #357ABD;
            }
            QPushButton:pressed {
                background-color: #2E6DA4;
            }
            QPushButton:disabled {
                background-color: #555;
                color: #999;
            }
        """)

        email_layout.addWidget(email_label)
        email_layout.addWidget(self.email_input)
        email_layout.addWidget(self.random_button)
        email_layout.addWidget(self.fetch_button)

        input_layout.addLayout(email_layout)

        # 使用次数显示
        self.usage_label = QLabel()
        self.usage_label.setStyleSheet("color: #AAA; font-size: 11px; margin-top: 4px;")
        input_layout.addWidget(self.usage_label)

        parent_layout.addWidget(input_group)
    
    def create_progress_section(self, parent_layout):
        """创建进度显示区域"""
        progress_group = QGroupBox("📊 获取状态")
        progress_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 12px;
                color: #4A90E2;
                border: 1px solid #555;
                border-radius: 6px;
                margin-top: 6px;
                padding-top: 6px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 6px 0 6px;
                background-color: #2B2B2B;
            }
        """)
        progress_layout = QVBoxLayout(progress_group)
        progress_layout.setSpacing(4)

        self.status_label = QLabel("🟢 就绪")
        self.status_label.setStyleSheet("font-weight: bold; color: #4CAF50; font-size: 12px;")

        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.progress_bar.setMaximumHeight(6)
        self.progress_bar.setStyleSheet("""
            QProgressBar {
                border: 1px solid #555;
                border-radius: 3px;
                background-color: #3A3A3A;
                text-align: center;
            }
            QProgressBar::chunk {
                background-color: #4A90E2;
                border-radius: 2px;
            }
        """)

        progress_layout.addWidget(self.status_label)
        progress_layout.addWidget(self.progress_bar)

        parent_layout.addWidget(progress_group)
    
    def create_content_section(self, parent_layout):
        """创建内容显示区域"""
        # 验证码显示区域（主要区域）
        codes_group = QGroupBox("🔑 激活额度验证码")
        codes_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 12px;
                color: #4CAF50;
                border: 2px solid #4CAF50;
                border-radius: 6px;
                margin-top: 6px;
                padding-top: 6px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 6px 0 6px;
                background-color: #2B2B2B;
            }
        """)
        codes_layout = QVBoxLayout(codes_group)

        self.codes_display = QTextEdit()
        self.codes_display.setMaximumHeight(120)
        self.codes_display.setPlaceholderText("🔍 激活额度验证码将在这里显示...")
        self.codes_display.setStyleSheet("""
            QTextEdit {
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 13px;
                background-color: #1E1E1E;
                border: 1px solid #555;
                border-radius: 4px;
                padding: 8px;
                color: #4CAF50;
                selection-background-color: #4A90E2;
            }
        """)

        codes_layout.addWidget(self.codes_display)

        # 邮件内容显示区域（较小）
        emails_group = QGroupBox("📧 额度详情")
        emails_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 12px;
                color: #FF9800;
                border: 1px solid #555;
                border-radius: 6px;
                margin-top: 6px;
                padding-top: 6px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 6px 0 6px;
                background-color: #2B2B2B;
            }
        """)
        emails_layout = QVBoxLayout(emails_group)

        self.emails_display = QTextEdit()
        self.emails_display.setMaximumHeight(100)
        self.emails_display.setPlaceholderText("📬 额度内容详情...")
        self.emails_display.setStyleSheet("""
            QTextEdit {
                font-size: 11px;
                background-color: #2A2A2A;
                border: 1px solid #555;
                border-radius: 4px;
                padding: 6px;
                color: #CCC;
                selection-background-color: #4A90E2;
            }
        """)

        emails_layout.addWidget(self.emails_display)

        parent_layout.addWidget(codes_group)
        parent_layout.addWidget(emails_group)
    
    def create_status_bar(self):
        """创建状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("请输入授权码")
    
    def get_stylesheet(self):
        """获取样式表 - Augment Code 深色主题"""
        return """
            QMainWindow {
                background-color: #2B2B2B;
                color: #E0E0E0;
            }
            QWidget {
                background-color: #2B2B2B;
                color: #E0E0E0;
            }
            QLabel {
                color: #E0E0E0;
            }
            QStatusBar {
                background-color: #1E1E1E;
                color: #AAA;
                border-top: 1px solid #555;
                font-size: 11px;
            }
            QTextEdit {
                background-color: #1E1E1E;
                border: 1px solid #555;
                border-radius: 4px;
                color: #E0E0E0;
                selection-background-color: #4A90E2;
            }
            QScrollBar:vertical {
                background-color: #3A3A3A;
                width: 12px;
                border-radius: 6px;
            }
            QScrollBar::handle:vertical {
                background-color: #555;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #666;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                border: none;
                background: none;
            }
        """
    
    def update_usage_display(self):
        """更新使用次数显示"""
        stats = self.usage_limiter.get_usage_stats()
        usage_text = f"今日已使用: {stats['today_usage']}/{stats['daily_limit']} 次，剩余: {stats['remaining']} 次"
        self.usage_label.setText(usage_text)
        
        # 如果没有剩余次数，禁用按钮
        if stats['remaining'] <= 0:
            self.fetch_button.setEnabled(False)
            self.fetch_button.setText("今日次数已用完")
        else:
            self.fetch_button.setEnabled(True)
            self.fetch_button.setText("🚀 获取激活额度")
    
    def fetch_quota(self):
        """获取额度"""
        display_email = self.email_input.text().strip()  # 显示的邮箱
        real_email = config.REAL_EMAIL  # 实际获取邮件的邮箱

        # 检查使用次数
        if not self.usage_limiter.can_use():
            QMessageBox.warning(self, "使用限制", "今日使用次数已达上限，请明天再试")
            return

        # 记录使用（记录显示的邮箱地址）
        if not self.usage_limiter.record_usage(display_email, "get_quota"):
            QMessageBox.warning(self, "使用限制", "记录使用次数失败")
            return

        # 更新使用次数显示
        self.update_usage_display()

        # 开始获取额度（使用真实邮箱地址）
        self.start_fetch_process(real_email)
    
    def start_fetch_process(self, email: str):
        """开始邮件获取过程"""
        # 禁用按钮
        self.fetch_button.setEnabled(False)
        self.fetch_button.setText("获取中...")
        
        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 不确定进度
        
        # 清空显示区域
        self.codes_display.clear()
        self.emails_display.clear()
        
        # 创建并启动工作线程
        self.worker = EmailFetchWorker(email)
        self.worker.progress_updated.connect(self.update_progress)
        self.worker.emails_received.connect(self.handle_emails_received)
        self.worker.error_occurred.connect(self.handle_error)
        self.worker.finished.connect(self.fetch_finished)
        
        self.worker.start()
    
    def update_progress(self, message: str):
        """更新进度显示"""
        if "连接" in message:
            icon = "🔄"
            color = "#FF9800"
        elif "获取" in message:
            icon = "📡"
            color = "#2196F3"
        elif "完成" in message:
            icon = "✅"
            color = "#4CAF50"
        else:
            icon = "ℹ️"
            color = "#4A90E2"

        self.status_label.setText(f"{icon} {message}")
        self.status_label.setStyleSheet(f"font-weight: bold; color: {color}; font-size: 12px;")
        self.status_bar.showMessage(f"{icon} {message}")
    
    def handle_emails_received(self, result: dict):
        """处理接收到的邮件"""
        mails = result.get('mails', [])
        
        if not mails:
            self.status_label.setText("📭 暂无激活额度")
            self.status_label.setStyleSheet("font-weight: bold; color: #FF9800; font-size: 12px;")
            self.emails_display.setText("📭 该邮箱暂无激活额度\n\n💡 提示：请确保已发送激活额度到此邮箱")
            return
        
        # 显示邮件内容
        self.display_emails(mails)
        
        # 提取验证码
        self.extract_and_display_codes(mails)
        
        self.status_label.setText(f"✅ 成功获取 {len(mails)} 个激活额度")
        self.status_label.setStyleSheet("font-weight: bold; color: #4CAF50; font-size: 12px;")
    
    def display_emails(self, mails: List[Dict]):
        """显示邮件内容"""
        content = []
        
        for i, mail in enumerate(mails, 1):
            content.append(f"=== 邮件 {i} ===")
            content.append(f"发件人: {mail.get('from', '未知')}")
            content.append(f"主题: {mail.get('subject', '无主题')}")
            content.append(f"时间: {mail.get('date', '未知')}")
            content.append(f"内容: {mail.get('text', mail.get('html', '无内容'))[:500]}...")
            content.append("")
        
        self.emails_display.setText("\n".join(content))
    
    def extract_and_display_codes(self, mails: List[Dict]):
        """提取并显示验证码"""
        all_codes = []
        
        for mail in mails:
            codes = self.code_extractor.extract_from_email(mail)
            all_codes.extend(codes)
        
        if all_codes:
            # 去重并按置信度排序
            unique_codes = {}
            for code_info in all_codes:
                code = code_info['code']
                if code not in unique_codes or code_info['confidence'] > unique_codes[code]['confidence']:
                    unique_codes[code] = code_info
            
            sorted_codes = sorted(unique_codes.values(), key=lambda x: x['confidence'], reverse=True)
            
            # 显示验证码
            display_content = []
            display_content.append("🔑 Augment Code 激活额度验证码:")
            display_content.append("=" * 35)
            display_content.append("")

            for i, code_info in enumerate(sorted_codes[:5], 1):  # 最多显示5个
                confidence_stars = "⭐" * int(code_info['confidence'] * 5)
                display_content.append(f"🎯 #{i}: {code_info['code']}")
                display_content.append(f"   信心度: {confidence_stars} ({code_info['confidence']:.2f})")
                display_content.append(f"   来源: {code_info.get('source', '邮件内容')}")
                display_content.append("")

            display_content.append("💡 提示：复制最高信心度的验证码使用")

            self.codes_display.setText("\n".join(display_content))
        else:
            self.codes_display.setText("❌ 未找到激活额度验证码\n\n🔍 可能原因:\n• 邮件中没有包含验证码\n• 验证码格式不被识别\n• 邮件内容为空或加载失败\n\n💡 建议:\n• 检查邮件是否为激活额度邮件\n• 稍后重试获取额度")
    
    def handle_error(self, error_message: str):
        """处理错误"""
        self.status_label.setText(f"❌ 错误: {error_message}")
        self.status_label.setStyleSheet("font-weight: bold; color: #F44336; font-size: 12px;")
        self.status_bar.showMessage(f"❌ 错误: {error_message}")

        # 创建深色主题的错误对话框
        msg = QMessageBox(self)
        msg.setIcon(QMessageBox.Critical)
        msg.setWindowTitle("Augment Code - 错误")
        msg.setText(f"获取激活额度时发生错误:\n\n{error_message}")
        msg.setStyleSheet("""
            QMessageBox {
                background-color: #2B2B2B;
                color: #E0E0E0;
            }
            QMessageBox QPushButton {
                background-color: #4A90E2;
                color: white;
                border: none;
                padding: 6px 16px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 60px;
            }
            QMessageBox QPushButton:hover {
                background-color: #357ABD;
            }
        """)
        msg.exec()
    
    def fetch_finished(self):
        """邮件获取完成"""
        # 隐藏进度条
        self.progress_bar.setVisible(False)
        
        # 恢复按钮状态
        self.fetch_button.setEnabled(True)
        self.fetch_button.setText("获取邮件")
        
        # 更新使用次数显示
        self.update_usage_display()
    
    def closeEvent(self, event):
        """窗口关闭事件"""
        if self.worker and self.worker.isRunning():
            self.worker.terminate()
            self.worker.wait()
        event.accept()
