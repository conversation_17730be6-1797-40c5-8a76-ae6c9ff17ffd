# 模块重构说明

## 📁 项目结构

```
augument-mail-plugin/
├── main.py                 # 应用程序入口
├── config.py              # 配置文件
├── main_window.py          # 主窗口控制器 (重构后，约300行)
├── auth_page.py            # 授权页面组件 (新增)
├── main_page.py            # 主功能页面组件 (新增)
├── tempmail_api.py         # TempMail API接口
├── code_extractor.py       # 验证码提取器
├── usage_limiter.py        # 使用次数限制器
├── main_window_backup.py   # 原主窗口备份 (753行)
└── 使用说明.md             # 使用说明文档
```

## 🔧 重构内容

### 1. **main_window.py** (原753行 → 现300行)
- **职责**: 主窗口控制器，负责页面切换和业务逻辑协调
- **主要功能**:
  - 窗口初始化和配置
  - 页面切换管理 (授权页面 ↔ 主功能页面)
  - 邮件获取线程管理
  - 信号连接和事件处理
  - 全局样式设置

### 2. **auth_page.py** (新增，约130行)
- **职责**: 授权验证页面组件
- **主要功能**:
  - 授权码输入界面
  - 授权验证逻辑
  - 错误提示显示
  - 页面状态重置

### 3. **main_page.py** (新增，约170行)
- **职责**: 主功能页面组件
- **主要功能**:
  - 邮箱输入和随机生成
  - 使用次数显示
  - 进度状态显示
  - 验证码和邮件内容显示
  - 获取按钮控制

## 🎯 重构优势

### 1. **代码可维护性**
- 单一职责原则：每个模块职责明确
- 代码行数合理：每个文件都在300行以内
- 逻辑清晰：UI组件与业务逻辑分离

### 2. **可扩展性**
- 组件化设计：页面组件可独立开发和测试
- 信号机制：组件间通过信号通信，耦合度低
- 样式分离：样式可以独立管理和修改

### 3. **开发效率**
- 模块化开发：多人可并行开发不同组件
- 易于调试：问题定位更精确
- 代码复用：组件可在其他项目中复用

## 🔄 信号流程

```
AuthPage.auth_success 
    ↓
MainWindow.on_auth_success() 
    ↓
切换到主页面 + 更新状态

MainPage.fetch_requested 
    ↓
MainWindow.start_fetch_process() 
    ↓
EmailFetchThread 执行
    ↓
MainPage 更新显示
```

## 📋 功能完整性

✅ **保持原有功能**:
- 授权验证 (授权码: 111111)
- 随机邮箱生成
- 邮件获取和验证码提取
- 使用次数限制
- 进度显示和状态反馈
- 深色主题样式

✅ **界面优化**:
- 授权码明文显示
- 标题文字完整显示
- 紧凑布局设计
- 响应式界面

## 🚀 后续扩展建议

1. **配置管理模块**: 将样式配置独立成文件
2. **主题切换**: 支持多种主题切换
3. **插件系统**: 支持验证码提取插件扩展
4. **国际化**: 支持多语言界面
5. **单元测试**: 为各个组件添加单元测试

## 📝 使用方式

重构后的使用方式与之前完全相同：

1. 运行 `python main.py`
2. 输入授权码 `111111`
3. 点击验证授权进入主界面
4. 生成随机邮箱或手动输入
5. 点击"获取激活额度"按钮

所有功能保持不变，但代码结构更加清晰和易于维护。
